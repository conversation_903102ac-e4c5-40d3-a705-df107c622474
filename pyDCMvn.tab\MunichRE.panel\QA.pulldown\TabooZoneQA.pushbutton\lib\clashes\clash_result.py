import clr
from DCMvn.core import DB, RevitExceptions
from DCMvn.revit.geometry import compute_oriented_bounding_box_solid, GetPointsFromGeometryObject

from arc_object import ArcObject
from mep_mass import MepMassObject, FloorMassObject

try:
    clr.AddReference('RevitNodes')
    import Revit

    clr.ImportExtensions(Revit.GeometryConversion)  # noqa
    clr.ImportExtensions(Revit.Elements)  # noqa
except:
    from DCMvn.forms import alert
    message = "There some issues with Revit Geometry\nPlease open Dynamo first then run the script again"
    alert(message, warn_icon=True, exitscript=True)


class SolidClashExecutor:
    """ Solid Clash Executor """
    def __init__(self, arc_object, mep_mass_object):
        # type: (ArcObject, MepMassObject) -> None
        self.__arc_object = arc_object  # type: ArcObject
        self.__mep_mass_object = mep_mass_object  # type: MepMassObject
        self.__arc_solid = arc_object.get_solid()
        self.__mep_mass_solid = mep_mass_object.get_solid()

    @property
    def is_geo_available(self):
        # type: () -> bool
        return self.__arc_solid is not None and self.__mep_mass_solid is not None

    @staticmethod
    def __get_solid_intersect_volume(solid1, solid2):
        # type: (DB.Solid, DB.Solid) -> tuple[bool, float]  # noqa
        intersect_solid = DB.BooleanOperationsUtils.ExecuteBooleanOperation(
            solid1, solid2, DB.BooleanOperationsType.Intersect
        )
        # ensure the intersect solid volume is more than 1/3 of the Mass solid volume
        # prevent false positive clash
        if intersect_solid and abs(intersect_solid.Volume) > solid2.Volume / 3:
            return True, intersect_solid.Volume
        return False, 0

    def check_clash(self):
        # type: () -> SolidClashResult
        """
        Check if the two solids are clashing
        Returns:
           SolidClashResult or None: SolidClashResult if clash detected
        """
        solid1 = self.__arc_solid
        solid2 = self.__mep_mass_solid
        try:
            is_clash, volume = self.__get_solid_intersect_volume(solid1, solid2)
            return SolidClashResult(self.__arc_object, self.__mep_mass_object, volume) if is_clash else None
        except RevitExceptions.InvalidOperationException:
            try:
                dyn_solid1 = solid1.ToProtoType()
                dyn_solid2 = solid2.ToProtoType()
                intersect_solid = dyn_solid1.Intersect(dyn_solid2)
                if intersect_solid:
                    intersect_solid = [i for i in intersect_solid if hasattr(i, "Volume")][0]
                    if intersect_solid.Volume > solid2.Volume / 3:
                        return SolidClashResult(self.__arc_object, self.__mep_mass_object, intersect_solid.Volume)
            except:
                try:
                    points1 = GetPointsFromGeometryObject(solid1)
                    points2 = GetPointsFromGeometryObject(solid2)
                    oriented_bbox_solid1 = compute_oriented_bounding_box_solid(points1)
                    oriented_bbox_solid2 = compute_oriented_bounding_box_solid(points2)
                    is_clash, volume = self.__get_solid_intersect_volume(oriented_bbox_solid1, oriented_bbox_solid2)
                    return SolidClashResult(self.__arc_object, self.__mep_mass_object, volume) if is_clash else None
                except:
                    # NOTE: return solid clash here to ensure that some ARC that failed to get solid not be ignored
                    return SolidClashResult(self.__arc_object, self.__mep_mass_object, 0)


class ClashResult:
    """ Base Clash Result """
    def __init__(self, mep_mass_object):
        # type: (MepMassObject) -> None
        self.__mep_mass_object = mep_mass_object

    @property
    def mep_mass_object(self):
        # type: () -> MepMassObject
        """
        Get the mass object in the clash result
        Returns:
            MepMassObject: mass object
        """
        return self.__mep_mass_object

    def set_guid(self):
        # type: () -> None
        """ Set the IFC GUID to the mass object """
        pass


class SolidClashResult(ClashResult):
    """ Solid Clash Result """
    def __init__(self, arc_object, mep_mass_object, intersect_volume=None):
        # type: (ArcObject, MepMassObject, float) -> None
        ClashResult.__init__(self, mep_mass_object)
        self.__arc_object = arc_object
        self.__intersect_volume = intersect_volume

    @property
    def arc_object(self):
        # type: () -> ArcObject
        """
        Get the arc object in the clash result
        Returns:
            ArcObject: arc object
        """
        return self.__arc_object

    @property
    def intersect_volume(self):
        # type: () -> float
        """
        Get the intersect volume of the two solids if execute solid intersection
        Returns:
            float: intersect volume
        """
        if not self.__intersect_volume:
            return 0.0
        return self.__intersect_volume

    def set_guid(self):
        # type: () -> None
        """ Set the IFC GUID to the mass object """
        self.mep_mass_object.set_ifc_guid(self.arc_object.guid)


class ColumnRayClashResult(ClashResult):
    """ Ray Clash Result """
    def __init__(self, arc_object, mep_mass_object):
        # type: (ArcObject, MepMassObject) -> None  # noqa
        ClashResult.__init__(self, mep_mass_object)
        self.__arc_object = arc_object  # type: ArcObject

    @property
    def arc_object(self):
        # type: () -> ArcObject
        """
        Get the arc object in the clash result
        Returns:
            ArcObject: arc object
        """
        return self.__arc_object

    def set_guid(self):
        # type: () -> None
        """ Set the IFC GUID to the mass object """
        self.mep_mass_object.set_ifc_guid(self.arc_object.guid)


class WallCornerRayClashResult(ClashResult):
    """ Wall Corner Ray Clash Result """
    def __init__(self, arc_objects, mep_mass_object):
        # type: (list[ArcObject], FloorMassObject) -> None  # noqa
        ClashResult.__init__(self, mep_mass_object)
        self.__arc_objects = arc_objects  # type: list[ArcObject]  # noqa

    @property
    def arc_objects(self):
        # type: () -> list[ArcObject]  # noqa
        """
        Get the arc objects in the clash result
        Returns:
            list[ArcObject]: arc objects
        """
        return self.__arc_objects

    def set_guid(self):
        # type: () -> None
        """
        Set the IFC GUID to the mass object
        Returns:
            str: joined guids with ";" separator
        """
        if not self.__arc_objects:
            guid = ""
        elif len(self.__arc_objects) > 1:
            guid =  ";".join([arc.guid for arc in self.arc_objects])
        else:
            guid = self.__arc_objects[0].guid

        self.mep_mass_object.set_ifc_guid(guid)
