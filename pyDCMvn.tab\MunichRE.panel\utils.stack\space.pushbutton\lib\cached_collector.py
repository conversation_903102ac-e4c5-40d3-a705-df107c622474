import clr
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import System
from DCMvn.forms.wpfforms import Select<PERSON>romList
from DCMvn.forms import alert
from space_props import GenericSpaceProperties

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class CachedCollector:
    """
    CachedCollector class to store and manage cached elements in Revit.
    This class collects various Revit elements and stores them in dictionaries
    for quick access.
    """

    def __init__(self):
        self.__level_dicts = self._collect_levels()
        self.__views_dicts = self._collect_views()
        self.__link_transform, self.__link_generic_space_doc = self._collect_linked_space()
        self.__linked_generic_spaces = self._collect_linked_generic_spaces()

    @property
    def level_dicts(self):
        return self.__level_dicts
    
    @property
    def views_dicts(self):
        return self.__views_dicts
    
    @property
    def link_transform(self):
        return self.__link_transform
    
    @property
    def link_generic_space_doc(self):
        return self.__link_generic_space_doc
    
    @property
    def linked_generic_spaces(self):
        """
        Get the linked generic spaces from the cached collector.
        
        Returns:
            list: A list of linked generic spaces.
        """
        return self.__linked_generic_spaces
    
    def _collect_linked_generic_spaces(self):
        """
        Collect linked generic space from the Revit document.
        
        Returns:
            tuple: A tuple containing the linked generic space, its transform, and the linked document.
        """
        return DB.FilteredElementCollector(self.__link_generic_space_doc) \
            .OfCategory(DB.BuiltInCategory.OST_GenericModel) \
            .WhereElementIsNotElementType() \
            .Where(lambda x: x.get_Parameter(GenericSpaceProperties.ExportToIfcAs).AsString() == GenericSpaceProperties.IfcSpaceType) \
            .ToList()

    def _collect_levels(self):
        return {
            i.Name: i
            for i in DB.FilteredElementCollector(HOST_APP.doc)
            .OfClass(DB.Level)
            .WhereElementIsNotElementType()
            .ToElements()
        }

    def _collect_views(self):
        return {
            i.Name: i
            for i in DB.FilteredElementCollector(HOST_APP.doc)
            .OfClass(DB.View)
            .Where(lambda x: x.ViewType == DB.ViewType.FloorPlan)
            .ToList()
        }

    def _collect_linked_space(self):
        link_generic_spaces = DB.FilteredElementCollector(HOST_APP.doc) \
                                .OfClass(DB.RevitLinkInstance) \
                                .WhereElementIsNotElementType() \
                                .ToElements()
                
        link_generic_space = SelectFromList.show(link_generic_spaces, title="Select Linked ARC", name_attr="Name", multiselect=False)
        if not link_generic_space:
            alert("No linked selected", exitscript=True, warn_icon=True)
        link_transform = link_generic_space.GetTotalTransform() if link_generic_space else None
        link_generic_space_doc = link_generic_space.GetLinkDocument() if link_generic_space else None

        return link_transform, link_generic_space_doc

# Create a singleton instance of CachedCollector
HOST_COLLECTOR = CachedCollector()