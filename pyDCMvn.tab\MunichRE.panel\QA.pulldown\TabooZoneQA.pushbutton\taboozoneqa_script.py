# coding: utf-8
import clr
import sys
import os
import System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

try:  # noqa
    extension_libs = IN[0]  # noqa  # type: ignore
    current_workspace = IN[1]  # noqa  # type: ignore
    sys.path.append(current_workspace) if current_workspace not in sys.path else None
    [sys.path.append(lib) for lib in extension_libs if os.path.exists(lib) and lib not in sys.path]
except NameError:
    current_workspace = os.path.dirname(__file__)

from DCMvn.revit.transaction import transaction_wrapper
from DCMvn.core import DYNAMO, HOST_APP
from DCMvn.forms import alert

if DYNAMO:
    clr.AddReference('RevitNodes')
    import Revit

    clr.ImportExtensions(Revit.GeometryConversion)  # noqa
    clr.ImportExtensions(Revit.Elements)  # noqa
else:
    from DCMvn.core import get_output

    output = get_output()

from lib.constant import HG_MODEL, O1_MODEL, HG_NEW_FLOOR_WORKSET
from lib.filter import get_condition, get_mass, get_arc_elements, get_arc_filter
from lib.check_execution import execute_solid_intersection, execute_raycast_intersection, execute_raycast_intersection_floor
from lib.clashes import SolidClashResult, ColumnRayClashResult, WallCornerRayClashResult
from lib.tests.ray_cast import test_arc_curve, test_single_arc_extend

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc


@transaction_wrapper(message="Transaction Message")
def main():
    out_value = []  # use this list to append out values for dynamo
    title = doc.Title
    is_hg_tbz = title.Contains(HG_MODEL)
    is_o1_tbz = title.Contains(O1_MODEL)

    if not is_hg_tbz and not is_o1_tbz:
        alert("Not supported model!\nPlease open {} or {} model"
              .format(HG_MODEL, O1_MODEL), warn_icon=True, exitscript=True)

    condition, link_instances = get_condition(doc, is_hg_tbz)
    condition_masses = get_mass(doc, condition)
    condition_arc_elements = get_arc_elements(link_instances)

    if condition is HG_NEW_FLOOR_WORKSET:
        # clashes, arc_solid_failed = execute_solid_intersection(condition_arc_elements=condition_arc_elements,
        #                                                        condition_masses=condition_masses)
        arc_filter = get_arc_filter(is_mass_floor_case=True)
        # return test_single_arc_extend(uidoc, arc_filter)
        clashes, mass_solid_failed = execute_raycast_intersection_floor(arc_filter=arc_filter,
                                                                        condition_masses=condition_masses,
                                                                        current_doc=doc)
    else:
        arc_filter = get_arc_filter()
        clashes, mass_solid_failed = execute_raycast_intersection(arc_filter=arc_filter,
                                                                  condition_masses=condition_masses,
                                                                  current_doc=doc)

    if DYNAMO:
        for clash in clashes:
            try:
                if isinstance(clash, SolidClashResult):
                    out_value.append((clash.arc_object.to_element(),
                                      clash.mep_mass_object.to_element(),
                                      clash.arc_object.get_solid().ToProtoType(),  # noqa
                                      clash.mep_mass_object.get_solid().ToProtoType(),
                                      clash.intersect_volume))
                elif isinstance(clash, ColumnRayClashResult):
                    out_value.append((clash.arc_object.to_element(),
                                      clash.mep_mass_object.to_element()))
                elif isinstance(clash, WallCornerRayClashResult):
                    out_value.append(([arc.to_element() for arc in clash.arc_objects],
                                      clash.mep_mass_object.to_element()))
            except Exception as e:
                print(e)
        return out_value
    else:
        clash_masses = []
        clash_arcs = []

        # 0️⃣ : Clashes found
        for clash in clashes:
            clash.set_guid()
            if isinstance(clash, SolidClashResult):
                output.print_md("Clash between **{}** and {} with volume: {}"
                      .format(clash.arc_object.guid,
                              output.linkify(clash.mep_mass_object.id),
                              clash.intersect_volume))
                clash_masses.append(clash.mep_mass_object)
                clash_arcs.append(clash.arc_object)

            elif isinstance(clash, ColumnRayClashResult):
                output.print_md("Clash between **{}** and {}"
                      .format(clash.arc_object.guid, output.linkify(clash.mep_mass_object.id)))
                clash_masses.append(clash.mep_mass_object)
                clash_arcs.append(clash.arc_object)

            elif isinstance(clash, WallCornerRayClashResult):
                output.print_md("Clash between **{}** and {}".format(
                    ", ".join([c.guid for c in clash.arc_objects]),
                    output.linkify(clash.mep_mass_object.id))
                )
                clash_masses.append(clash.mep_mass_object)
                clash_arcs.extend(clash.arc_objects)

        # 1️⃣ : Mass object cannot get solid
        for mass_failed in mass_solid_failed:
            print("Mass solid failed: {}"
                  .format(output.linkify(mass_failed.id, mass_failed.id)))

        # 2️⃣ : MEP masses can get solid but failed to clash
        clash_mass_ids = [m.mep_mass_object.id for m in clashes]
        missing_masses = [m_ for m_ in condition_masses if m_.id not in clash_mass_ids and m_ not in mass_solid_failed]
        for m_m in missing_masses:
            print("Mass failed: {}"
                  .format(output.linkify(m_m.id)))

        # 3️⃣ : Arc object failed to clash
        clash_arc_failed = [arc for arc in condition_arc_elements if arc not in clash_arcs]
        for arc_clash in clash_arc_failed:
            output.print_md("ARC failed: **{}**"
                  .format(arc_clash.guid))

        # 4️⃣ : Duplicate ARC clashes
        duplicate_arcs = [i for i in set(clash_arcs) if clash_arcs.count(i) > 1]
        for arc in duplicate_arcs:
            mep_masses = [m for m in clashes if m.arc_object == arc]
            output.print_md("Duplicate ARC clash: **{}** with {}"
                  .format(arc.guid,
                          [output.linkify(m.mep_mass_object.id) for m in mep_masses]))

        # 4️⃣ : Total clashes found
        print("Total ARC elements: {}".format(len(condition_arc_elements)))
        print("Total MEP masses: {}".format(len(condition_masses)))
        print("Total clashes found: {}".format(len(clashes)))

        # 5️⃣ : Total Mass & ARC elements failed
        print("Total Mass elements failed to get solid: {}".format(len(mass_solid_failed)))
        print("Total ARC elements failed to clash: {}".format(len(clash_arc_failed)))
        print("Total MEP masses failed to clash: {}".format(len(missing_masses)))
        print("Total duplicate ARC clashes: {}".format(len(duplicate_arcs)))

        return "DCMvn"

OUT = main()
if not DYNAMO:
    output.close_others()  # noqa
