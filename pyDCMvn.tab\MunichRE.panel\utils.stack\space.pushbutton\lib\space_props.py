# coding: utf-8
from DCMvn.core import DB

class SpaceProperties:
    Guid = DB.BuiltInParameter.IFC_GUID
    Name = DB.BuiltInParameter.ROOM_NAME
    Number = DB.BuiltInParameter.ROOM_NUMBER
    BaseOffset = DB.BuiltInParameter.ROOM_LOWER_OFFSET
    LimitOffset = DB.BuiltInParameter.ROOM_UPPER_OFFSET
    Level = DB.BuiltInParameter.ROOM_LEVEL_ID
    UpperLimit = DB.BuiltInParameter.ROOM_UPPER_LEVEL

class GenericSpaceProperties:
    Guid = DB.BuiltInParameter.IFC_GUID
    ExportToIfcAs = DB.BuiltInParameter.IFC_EXPORT_ELEMENT_AS
    IfcSpaceType = "IfcSpaceType"
    Name = "AW_ARC.Raumname"
    Number = "AW_ARC.Raumnummer ARC"
    Level = "AW_ARC.Geschossnummer"