# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System

from lib.cached_collector import HOST_COLLECTOR
from lib.intersection import Intersection<PERSON>hecker

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

# Print header
output.print_html('<h1 style="color:blue; text-align:center;">🏠 Room Number Assignment Tool</h1>')
output.print_html('<div style="text-align:center; margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 5px;">')
output.print_html('<strong>Mapping MEP elements to room properties...</strong>')
output.print_html('</div>')

# Initialize data collection
output.print_html('<h3 style="color:blue;">📊 Initializing Data Collection</h3>')
output.print_html('Loading rooms and MEP elements...')

generic_spaces = HOST_COLLECTOR.linked_generic_spaces
current_model_elements = HOST_COLLECTOR.current_model_elements
target_parameters = HOST_COLLECTOR.target_parameter
source_parameter = HOST_COLLECTOR.source_parameter

output.print_html('<strong style="color:green;">✓ Loaded {} rooms and {} MEP elements</strong>'.format(
    len(generic_spaces), len(current_model_elements)))

# Initialize intersection checker
output.insert_divider()
output.print_html('<h3 style="color:blue;">🔍 Checking Intersections</h3>')
output.print_html('Analyzing spatial relationships between rooms and MEP elements...')

intersection_checker = IntersectionChecker(generic_spaces, current_model_elements)
intersection_checker.check_intersection(HOST_COLLECTOR.link_transform)

with DB.Transaction(doc, "Set Room Numbers") as t:
    t.Start()

    # Get initial statistics
    stats = intersection_checker.get_statistics()

    if not intersection_checker.match_set:
        output.print_html('<strong style="color:orange;">⚠️ No intersections found between rooms and MEP elements.</strong>')
    else:
        # Display the matched pairs
        output.print_html('<strong style="color:green;">✓ Found {} intersections</strong>'.format(len(intersection_checker.match_set)))

        # Process parameter assignments
        output.insert_divider()
        output.print_html('<h3 style="color:blue;">⚙️ Setting Parameters</h3>')
        output.print_html('Assigning room numbers to MEP elements...')

        intersection_checker.set_parameters(source_parameter, target_parameters)

        # Display successful mappings
        final_stats = intersection_checker.get_statistics()
        if final_stats['successful_mappings'] > 0:
            output.print_html('<strong style="color:green;">✓ Successfully mapped {} MEP elements to rooms</strong>'.format(
                final_stats['successful_mappings']))

        if final_stats['unmapped_mep_elements'] > 0:
            output.print_html('<strong style="color:orange;">⚠️ Failed to map {} MEP elements</strong>'.format(
                len(final_stats['unmapped_mep_elements'])))

    t.Commit()

# Generate comprehensive statistics report
output.insert_divider()
output.print_html('<h2 style="color:blue; border-bottom: 2px solid #007acc; padding-bottom: 5px;">📋 OPERATION SUMMARY</h2>')

# Get final statistics
final_stats = intersection_checker.get_statistics()

# Create summary table
output.print_html('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">')
output.print_html('<table style="width: 100%; border-collapse: collapse;">')
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Total rooms processed:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>'.format(
    final_stats['total_rooms']))
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Total MEP elements found:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>'.format(
    final_stats['total_mep_elements']))
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">MEP elements successfully mapped:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'.format(
    "green" if final_stats['successful_mappings'] > 0 else "gray", final_stats['successful_mappings']))
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Failed MEP element mappings:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'.format(
    "red" if final_stats['unmapped_mep_elements'] > 0 else "gray", len(final_stats['unmapped_mep_elements'])))
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Empty rooms (no MEP elements):</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'.format(
    "orange" if final_stats['empty_rooms'] > 0 else "gray", final_stats['empty_rooms']))
output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Rooms containing MEP elements:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'.format(
    "green" if final_stats['rooms_with_mep'] > 0 else "gray", final_stats['rooms_with_mep']))
output.print_html('</table>')
output.print_html('</div>')

# Display unmapped MEP elements with linkify
if final_stats['unmapped_mep_elements']:
    output.print_html('<div style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0;">')
    output.print_html('<h4 style="color: #856404; margin-top: 0;">⚠️ Unmapped MEP Elements</h4>')
    output.print_html('<p style="margin-bottom: 10px;">The following MEP elements could not be mapped to room properties:</p>')

    for mep_element in final_stats['unmapped_mep_elements']:
        output.print_html('• {}'.format(output.linkify(mep_element.Id)))

    output.print_html('</div>')

# Final status message
output.insert_divider()
if final_stats['successful_mappings'] > 0:
    output.print_html('<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 5px; border: 2px solid #28a745;">')
    output.print_html('<h2 style="color:green; margin: 0;">🎉 Room Number Assignment Completed Successfully!</h2>')
    output.print_html('<p style="margin: 5px 0;">Successfully mapped {} MEP elements to rooms.</p>'.format(final_stats['successful_mappings']))
    output.print_html('</div>')
else:
    output.print_html('<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #f8d7da; border-radius: 5px; border: 2px solid #dc3545;">')
    output.print_html('<h2 style="color:red; margin: 0;">⚠️ No Room Numbers Assigned</h2>')
    output.print_html('<p style="margin: 5px 0;">No MEP elements were successfully mapped to rooms. Check the output above for details.</p>')
    output.print_html('</div>')

# Close other output windows for cleaner presentation
output.close_others()
    
