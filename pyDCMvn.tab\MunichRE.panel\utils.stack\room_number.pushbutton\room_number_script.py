# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System

from lib.cached_collector import HOST_COLLECTOR
from lib.intersection import <PERSON><PERSON><PERSON><PERSON><PERSON>
from lib.space_props import RoomNumberToolConstants

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

# Print header
output.print_html(RoomNumberToolConstants.TOOL_TITLE)
output.print_html(RoomNumberToolConstants.TOOL_DESCRIPTION)

# Initialize data collection
output.print_html(RoomNumberToolConstants.SECTION_HEADERS['DATA_COLLECTION'])
output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['LOADING_DATA'])

generic_spaces = HOST_COLLECTOR.linked_generic_spaces
current_model_elements = HOST_COLLECTOR.current_model_elements
target_parameters = HOST_COLLECTOR.target_parameter
source_parameter = HOST_COLLECTOR.source_parameter

output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['DATA_LOADED'].format(
    len(generic_spaces), len(current_model_elements)))

# Initialize intersection checker
output.insert_divider()
output.print_html(RoomNumberToolConstants.SECTION_HEADERS['INTERSECTION_CHECK'])
output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['ANALYZING_RELATIONSHIPS'])

intersection_checker = IntersectionChecker(generic_spaces, current_model_elements)
intersection_checker.check_intersection(HOST_COLLECTOR.link_transform)

with DB.Transaction(doc, "Set Room Numbers") as t:
    t.Start()

    # Get initial statistics
    stats = intersection_checker.get_statistics()

    if not intersection_checker.match_set:
        output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['NO_INTERSECTIONS'])
    else:
        # Display the matched pairs
        output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['INTERSECTIONS_FOUND'].format(len(intersection_checker.match_set)))

        # Process parameter assignments
        output.insert_divider()
        output.print_html(RoomNumberToolConstants.SECTION_HEADERS['PARAMETER_SETTING'])

        # Display parameter information
        if source_parameter and target_parameters:
            output.print_html(RoomNumberToolConstants.HTML_STYLES['PARAMETER_INFO_BOX'])
            output.print_html(RoomNumberToolConstants.PARAMETER_MAPPING_MESSAGE.format(
                source_parameter.name, target_parameters.name))
            output.print_html(RoomNumberToolConstants.HTML_STYLES['CLOSE_DIV'])

        output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['ASSIGNING_PARAMETERS'])

        intersection_checker.set_parameters(source_parameter, target_parameters)

        # Display successful mappings
        final_stats = intersection_checker.get_statistics()
        if final_stats['successful_mappings'] > 0:
            output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['SUCCESSFUL_MAPPING'].format(
                final_stats['successful_mappings']))

        if final_stats['failed_mappings'] > 0:
            output.print_html(RoomNumberToolConstants.STATUS_MESSAGES['FAILED_MAPPING'].format(
                final_stats['failed_mappings']))

    t.Commit()

# Generate comprehensive statistics report
output.insert_divider()
output.print_html(RoomNumberToolConstants.SECTION_HEADERS['OPERATION_SUMMARY'])

# Get final statistics
final_stats = intersection_checker.get_statistics()

# Create summary table
output.print_html(RoomNumberToolConstants.HTML_STYLES['SUMMARY_TABLE_CONTAINER'])
output.print_html(RoomNumberToolConstants.HTML_STYLES['SUMMARY_TABLE'])
output.print_html(RoomNumberToolConstants.TABLE_ROWS['TOTAL_ROOMS'].format(
    final_stats['total_rooms']))
output.print_html(RoomNumberToolConstants.TABLE_ROWS['TOTAL_MEP'].format(
    final_stats['total_mep_elements']))
output.print_html(RoomNumberToolConstants.TABLE_ROWS['SUCCESSFUL_MAPPINGS'].format(
    "green" if final_stats['successful_mappings'] > 0 else "gray", final_stats['successful_mappings']))
output.print_html(RoomNumberToolConstants.TABLE_ROWS['FAILED_MAPPINGS'].format(
    "red" if final_stats['failed_mappings'] > 0 else "gray", final_stats['failed_mappings']))
output.print_html(RoomNumberToolConstants.TABLE_ROWS['EMPTY_ROOMS'].format(
    "orange" if final_stats['empty_rooms'] > 0 else "gray", final_stats['empty_rooms']))
output.print_html(RoomNumberToolConstants.TABLE_ROWS['ROOMS_WITH_MEP'].format(
    "green" if final_stats['rooms_with_mep'] > 0 else "gray", final_stats['rooms_with_mep']))
output.print_html('</table>')
output.print_html(RoomNumberToolConstants.HTML_STYLES['CLOSE_DIV'])

# Display unmapped MEP elements with linkify
if final_stats['unmapped_mep_elements']:
    output.print_html(RoomNumberToolConstants.HTML_STYLES['UNMAPPED_ELEMENTS_BOX'])
    output.print_html(RoomNumberToolConstants.UNMAPPED_ELEMENTS['HEADER'])
    output.print_html(RoomNumberToolConstants.UNMAPPED_ELEMENTS['DESCRIPTION'])

    for mep_element in final_stats['unmapped_mep_elements']:
        output.print_html(RoomNumberToolConstants.UNMAPPED_ELEMENTS['BULLET_POINT'].format(output.linkify(mep_element.Id)))

    output.print_html(RoomNumberToolConstants.HTML_STYLES['CLOSE_DIV'])

# Final status message
output.insert_divider()
if final_stats['successful_mappings'] > 0:
    output.print_html(RoomNumberToolConstants.HTML_STYLES['SUCCESS_FINAL_BOX'])
    output.print_html(RoomNumberToolConstants.FINAL_STATUS['SUCCESS_TITLE'])
    output.print_html(RoomNumberToolConstants.FINAL_STATUS['SUCCESS_DESCRIPTION'].format(final_stats['successful_mappings']))
    output.print_html(RoomNumberToolConstants.HTML_STYLES['CLOSE_DIV'])
else:
    output.print_html(RoomNumberToolConstants.HTML_STYLES['ERROR_FINAL_BOX'])
    output.print_html(RoomNumberToolConstants.FINAL_STATUS['ERROR_TITLE'])
    output.print_html(RoomNumberToolConstants.FINAL_STATUS['ERROR_DESCRIPTION'])
    output.print_html(RoomNumberToolConstants.HTML_STYLES['CLOSE_DIV'])

# Close other output windows for cleaner presentation
output.close_others()
    
