# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import Trace, System

from lib.cached_collector import HOST_COLLECTOR
from lib.intersection import Inter<PERSON><PERSON>he<PERSON>

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

# # link_transform
# link_transform = HOST_COLLECTOR.link_transform

# # base
# projectBasepoint = DB.BasePoint.GetProjectBasePoint(doc).Position
# base_transform = DB.Transform.CreateTranslation(projectBasepoint).Inverse

# # shared
# shared_transform = doc.ActiveProjectLocation.GetTotalTransform().Inverse

# # user input
# a = DSelection.pick(is_linked=True)
# origin_box = a.get_BoundingBox(None)
# origin_box_transform = origin_box.Transform.Inverse


# newBox = DB.BoundingBoxXYZ()
# ori_min_transform = link_transform.OfPoint(origin_box.Min)
# ori_max_transform = link_transform.OfPoint(origin_box.Max)
# # newBox.Min = base_transform.OfPoint(ori_min_transform)
# # newBox.Max = base_transform.OfPoint(ori_max_transform)
# newBox.Min = ori_min_transform
# newBox.Max = ori_max_transform

# # solid  = get_element_merged_solid(a)
# Trace.Write(newBox)

generic_spaces = HOST_COLLECTOR.linked_generic_spaces
current_model_elements = HOST_COLLECTOR.current_model_elements
target_parameters = HOST_COLLECTOR.target_parameter
source_parameter = HOST_COLLECTOR.source_parameter

intersection_checker = IntersectionChecker(generic_spaces, current_model_elements)
intersection_checker.check_intersection(HOST_COLLECTOR.link_transform)

with DB.Transaction(doc, "Set Room Numbers") as t:
    t.Start()
    
    if not intersection_checker.match_set:
        output.print_md("No intersections found between rooms and MEP elements.")
    else:
        # Display the matched pairs
        output.print_md("Found {} intersections:".format(len(intersection_checker.match_set)))
        for room, mep_element in intersection_checker.match_set:
            output.print_md("- Room: {0}, MEP Element: {1}".format(room.Id, mep_element.Id))
        intersection_checker.set_parameters(source_parameter, target_parameters)
    
    t.Commit()





    
    
