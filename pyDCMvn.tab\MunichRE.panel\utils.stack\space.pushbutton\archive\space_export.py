# coding: utf8
import clr
import os

from DCMvn.core import DB, HOST_APP
from DCMvn.io import save_excel_file
from DCMvn.forms import alert
from DCMvn.core.framework import System
from MunichRE.excel_reader import MiniExcel

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# Add Dictionary and OrderedDictionary support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Generic import Dictionary
from System.Collections.Specialized import OrderedDictionary

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

MreProjectIdentifier = "0061721_MRE"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
LevelParameter = "AW_ARC.Geschossnummer"
SpaceNameParameter = "AW_ARC.Raumname"
SpaceNumberParameter = "AW_ARC.Raumnummer ARC"
SpaceAreaParameter = "AW_ARC.Raumfläche Berechnet WoFlV"

def get_link_data(document):
    link_instance = (DB.FilteredElementCollector(document)
                     .OfClass(DB.RevitLinkInstance)
                     .WhereElementIsNotElementType()
                     .FirstOrDefault(lambda x: x.Name.Contains(MreProjectIdentifier)))  # type: DB.RevitLinkInstance

    return link_instance.GetLinkDocument()

def get_generic_spaces(document):
    collector = DB.FilteredElementCollector(document)
    spaces = (collector
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue)
              .ToList())
    return spaces

def get_space_data(space):
    space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
    space_name = space.get_Parameter(DB.BuiltInParameter.ROOM_NAME).AsString()
    space_number = space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).AsString()
    space_area = space.get_Parameter(DB.BuiltInParameter.ROOM_AREA).AsDouble()
    # Convert area to m² with 3 decimal places
    space_area_formatted = "{:.3f} m2".format(DB.UnitUtils.ConvertFromInternalUnits(space_area, DB.UnitTypeId.SquareMeters))
    space_level = space.get_Parameter(DB.BuiltInParameter.LEVEL_NAME).AsString()
    
    wti = DB.WorksharingUtils.GetWorksharingTooltipInfo(doc, space.Id)
    status = "Auto Place" if wti.LastChangedBy.Contains("055") else "Manual Place or Edit"

    return {
        space_guid: {
            "Space Name": space_name,
            "Space Number": space_number,
            "Guid": space_guid,
            "Level Reference": space_level,
            "Area": space_area_formatted,
            "Raw Area": space_area,  # Keep raw value for internal calculations
            "status": status
        }
    }
    
def get_ifcSpace_data(space):
    space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
    space_name = space.LookupParameter(SpaceNameParameter).AsString()
    space_number = space.LookupParameter(SpaceNumberParameter).AsString()
    space_area = space.LookupParameter(SpaceAreaParameter).AsDouble()
    # Convert area to m² with 3 decimal places and Convert to Square Meters
    space_area_formatted = "{:.3f} m2".format(DB.UnitUtils.ConvertFromInternalUnits(space_area, DB.UnitTypeId.SquareMeters))
    space_level = space.LookupParameter(LevelParameter).AsString()
    
    return {
        space_guid: {
            "Space Name": space_name,
            "Space Number": space_number,
            "RGuid": space_guid,
            "Level Reference": space_level,
            "Area": space_area_formatted,
            "Raw Area": space_area,  # Keep raw value for internal calculations
            "status": "IFC Space"
        }
    }

def get_warning_data(warning):
    """
    Convert a FailureMessage warning to a dictionary with warning details
    """
    warning_data = {
        "Description": warning.GetDescriptionText(),
        "Severity": warning.GetSeverity().ToString(),
        "Resolution": warning.GetDefaultResolutionCaption() if warning.HasResolutions() else ""
    }
    
    # Get failing elements
    failing_elements = warning.GetFailingElements()
    additional_elements = warning.GetAdditionalElements()
    
    if failing_elements.Count > 0:
        element_ids = [str(elem_id.IntegerValue) for elem_id in failing_elements]
        warning_data["Failing Elements"] = " - ".join(element_ids)
    elif additional_elements.Count > 0:
        element_ids = [str(elem_id.IntegerValue) for elem_id in additional_elements]
        warning_data["Failing Elements"] = " - ".join(element_ids)
    else:
        warning_data["Failing Elements"] = ""
    
    return warning_data

def get_space_warnings(document):
    """
    Get all warnings from the document and map them to space elements
    """
    print("=== ANALYZING REVIT WARNINGS ===")
    
    # Get all warnings from document
    warnings = document.GetWarnings()
    print("Total warnings found: {}".format(len(warnings)))
    
    # Dictionary to store space ID -> list of warning descriptions
    space_warnings = {}
    
    # Get all MEP spaces for reference
    spaces = DB.FilteredElementCollector(document).OfCategory(DB.BuiltInCategory.OST_MEPSpaces).WhereElementIsNotElementType().ToElements()
    space_ids = set([space.Id.IntegerValue for space in spaces])
    
    warning_count = 0
    space_warning_count = 0
    
    for warning in warnings:
        warning_data = get_warning_data(warning)
        warning_count += 1
        
        # Get all failing and additional elements
        all_failing_elements = []
        
        failing_elements = warning.GetFailingElements()
        for elem_id in failing_elements:
            all_failing_elements.append(elem_id.IntegerValue)
        
        additional_elements = warning.GetAdditionalElements()
        for elem_id in additional_elements:
            all_failing_elements.append(elem_id.IntegerValue)
        
        # Check if any failing element is a space
        space_found = False
        for element_id in all_failing_elements:
            if element_id in space_ids:
                space_found = True
                if element_id not in space_warnings:
                    space_warnings[element_id] = []
                
                # Extract key information from warning description
                description = warning_data["Description"]
                severity = warning_data["Severity"]
                
                # Create a concise warning summary
                warning_summary = description
                if "overlap" in description.lower():
                    warning_summary = "Overlap"
                elif "duplicate" in description.lower():
                    warning_summary = "Duplicate Number"
                elif "enclosed" in description.lower() or "not properly enclosed" in description.lower():
                    warning_summary = "Not Enclosed"
                elif "redundant" in description.lower():
                    warning_summary = "Redundant"
                elif "unplaced" in description.lower():
                    warning_summary = "Unplaced"
                elif "area" in description.lower() and "computation" in description.lower():
                    warning_summary = "Area Computation Issue"
                elif "boundary" in description.lower():
                    warning_summary = "Boundary Issue"
                else:
                    # Use first 30 characters if no specific pattern
                    warning_summary = description[:30] + "..." if len(description) > 50 else description
                
                space_warnings[element_id].append(warning_summary)
        
        if space_found:
            space_warning_count += 1
    
    print("Warnings affecting spaces: {}".format(space_warning_count))
    print("Spaces with warnings: {}".format(len(space_warnings)))
    
    # Convert space warnings to GUID-based dictionary
    space_warnings_by_guid = {}
    
    for space in spaces:
        space_id = space.Id.IntegerValue
        if space_id in space_warnings:
            try:
                space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
                if space_guid and space_guid.strip():
                    # Join warnings with semicolon
                    unique_warnings = list(set(space_warnings[space_id]))  # Remove duplicates
                    warning_text = ";".join(unique_warnings)
                    space_warnings_by_guid[space_guid] = warning_text
            except:
                continue  # Skip spaces without GUID
    
    print("Spaces with warnings (by GUID): {}".format(len(space_warnings_by_guid)))
    print("=" * 50)
    
    return space_warnings_by_guid

def create_ordered_row(key_value_pairs):
    """
    Create an OrderedDictionary from a list of (key, value) tuples to ensure column order
    """
    ordered_dict = OrderedDictionary()
    for key, value in key_value_pairs:
        ordered_dict[key] = value
    return ordered_dict

def compare_spaces(ifc_spaces_data, spaces_data):
    """
    Compare IFC spaces with Revit spaces and format data according to table layout.
    """
    # Get space warnings first
    space_warnings = get_space_warnings(doc)
    
    comparison_results = []
    
    # Convert list of dictionaries to a flat structure for easier lookup
    ifc_spaces_dict = {}
    revit_spaces_dict = {}
    
    for ifc_space_dict in ifc_spaces_data:
        for guid, data in ifc_space_dict.items():
            ifc_spaces_dict[guid] = data
    
    for revit_space_dict in spaces_data:
        for guid, data in revit_space_dict.items():
            revit_spaces_dict[guid] = data
    
    # Process all IFC spaces
    for guid, ifc_data in ifc_spaces_dict.items():
        # Define column order explicitly
        row_data = [
            # IFC Data (columns A-E)
            ("IFC Room Name", ifc_data["Space Name"]),
            ("IFC Room Number", ifc_data["Space Number"]),
            ("IFC GUID", guid),
            ("IFC Level Reference", ifc_data["Level Reference"]),
            ("IFC Area", ifc_data["Area"]),
            
            # Status/Reflection Info (columns F-G)
            ("Reflect into RVT", "Not"),
            ("Reason", "No"),
            
            # RVT Data (columns H-L) - defaults
            ("RVT Space Name", ""),
            ("RVT Space Number", ""),
            ("RVT GUID", ""),
            ("RVT Level Reference", ""),
            ("RVT Area", ""),
            
            # Additional Info (columns M-N)
            ("Status", ""),
            ("Issues", "")
        ]
        
        # Update RVT data if exists
        revit_data = revit_spaces_dict.get(guid)
        if revit_data:
            # Update the row data with Revit information
            row_data[5] = ("Reflect into RVT", "Done")
            row_data[7] = ("RVT Space Name", revit_data["Space Name"])
            row_data[8] = ("RVT Space Number", revit_data["Space Number"])
            row_data[9] = ("RVT GUID", revit_data["Guid"])
            row_data[10] = ("RVT Level Reference", revit_data["Level Reference"])
            row_data[11] = ("RVT Area", revit_data["Area"])
            row_data[12] = ("Status", revit_data["status"])
            row_data[13] = ("Issues", space_warnings.get(guid, ""))
            
            # Compare areas and note differences
            if ifc_data["Area"] != revit_data["Area"]:
                row_data[6] = ("Reason", "Area mismatch")
        
        # Create ordered row
        ordered_row = create_ordered_row(row_data)
        comparison_results.append(ordered_row)
    
    # Find Revit spaces that don't exist in IFC
    for guid, revit_data in revit_spaces_dict.items():
        if guid not in ifc_spaces_dict:
            row_data = [
                # Empty IFC data
                ("IFC Room Name", ""),
                ("IFC Room Number", ""),
                ("IFC GUID", ""),
                ("IFC Level Reference", ""),
                ("IFC Area", ""),
                ("Reflect into RVT", "N/A"),
                ("Reason", "Revit space has no IFC counterpart"),
                
                # Revit data
                ("RVT Space Name", revit_data["Space Name"]),
                ("RVT Space Number", revit_data["Space Number"]),
                ("RVT GUID", revit_data["Guid"]),
                ("RVT Level Reference", revit_data["Level Reference"]),
                ("RVT Area", revit_data["Area"]),
                ("Status", revit_data["status"]),
                ("Issues", space_warnings.get(guid, ""))
            ]
            
            ordered_row = create_ordered_row(row_data)
            comparison_results.append(ordered_row)
    
    return comparison_results

def debug_space_matching(ifc_spaces_data, spaces_data):
    """
    Debug function to analyze space matching between IFC and Revit
    """
    # Flatten data structures
    ifc_spaces_dict = {}
    revit_spaces_dict = {}
    
    for ifc_space_dict in ifc_spaces_data:
        for guid, data in ifc_space_dict.items():
            ifc_spaces_dict[guid] = data
    
    for revit_space_dict in spaces_data:
        for guid, data in revit_space_dict.items():
            revit_spaces_dict[guid] = data
    
    print("=== SPACE MATCHING DEBUG REPORT ===")
    print("Total IFC Spaces: {}".format(len(ifc_spaces_dict)))
    print("Total Revit Spaces: {}".format(len(revit_spaces_dict)))
    
    # Check for perfect matches
    perfect_matches = []
    guid_matches = []
    mismatched_data = []
    ifc_only = []
    revit_only = []
    
    for guid, ifc_data in ifc_spaces_dict.items():
        if guid in revit_spaces_dict:
            revit_data = revit_spaces_dict[guid]
            guid_matches.append(guid)
            
            # Check if all data matches
            name_match = ifc_data["Space Name"] == revit_data["Space Name"]
            number_match = ifc_data["Space Number"] == revit_data["Space Number"]
            level_match = ifc_data["Level Reference"] == revit_data["Level Reference"]
            area_match = ifc_data["Area"] == revit_data["Area"]
            
            if name_match and number_match and level_match and area_match:
                perfect_matches.append(guid)
            else:
                # Create readable match status string
                match_status = []
                if not name_match:
                    match_status.append("Name")
                if not number_match:
                    match_status.append("Number")
                if not level_match:
                    match_status.append("Level")
                if not area_match:
                    match_status.append("Area")
                
                mismatch_summary = "Mismatch: " + ";".join(match_status)
                
                mismatched_data.append({
                    "GUID": guid,
                    "IFC_Name": ifc_data["Space Name"],
                    "RVT_Name": revit_data["Space Name"],
                    "IFC_Number": ifc_data["Space Number"],
                    "RVT_Number": revit_data["Space Number"],
                    "IFC_Level": ifc_data["Level Reference"],
                    "RVT_Level": revit_data["Level Reference"],
                    "IFC_Area": ifc_data["Area"],
                    "RVT_Area": revit_data["Area"],
                    "Mismatch_Summary": mismatch_summary,
                    "Name_Match": "Yes" if name_match else "No",
                    "Number_Match": "Yes" if number_match else "No",
                    "Level_Match": "Yes" if level_match else "No",
                    "Area_Match": "Yes" if area_match else "No"
                })
        else:
            ifc_only.append({
                "GUID": guid,
                "Name": ifc_data["Space Name"],
                "Number": ifc_data["Space Number"],
                "Level": ifc_data["Level Reference"],
                "Area": ifc_data["Area"]
            })
    
    # Find Revit-only spaces
    for guid, revit_data in revit_spaces_dict.items():
        if guid not in ifc_spaces_dict:
            revit_only.append({
                "GUID": guid,
                "Name": revit_data["Space Name"],
                "Number": revit_data["Space Number"],
                "Level": revit_data["Level Reference"],
                "Area": revit_data["Area"]
            })
    
    # Print summary
    if len(ifc_spaces_dict) > 0:
        perfect_percentage = len(perfect_matches)/float(len(ifc_spaces_dict))*100
    else:
        perfect_percentage = 0
    
    print("Perfect Matches: {} ({:.1f}%)".format(len(perfect_matches), perfect_percentage))
    print("GUID Matches with Data Differences: {}".format(len(mismatched_data)))
    print("IFC Spaces NOT in Revit: {}".format(len(ifc_only)))
    print("Revit Spaces NOT in IFC: {}".format(len(revit_only)))
    
    # Detailed breakdown by level
    ifc_by_level = {}
    revit_by_level = {}
    
    for guid, data in ifc_spaces_dict.items():
        level = data["Level Reference"]
        if level not in ifc_by_level:
            ifc_by_level[level] = []
        ifc_by_level[level].append(guid)
    
    for guid, data in revit_spaces_dict.items():
        level = data["Level Reference"]
        if level not in revit_by_level:
            revit_by_level[level] = []
        revit_by_level[level].append(guid)
    
    print("\n=== LEVEL BREAKDOWN ===")
    all_levels = set(list(ifc_by_level.keys()) + list(revit_by_level.keys()))
    for level in sorted(all_levels):
        ifc_count = len(ifc_by_level.get(level, []))
        revit_count = len(revit_by_level.get(level, []))
        print("Level {}: IFC={}, Revit={}".format(level, ifc_count, revit_count))
    
    return {
        "perfect_matches": perfect_matches,
        "guid_matches": guid_matches,
        "mismatched_data": mismatched_data,
        "ifc_only": ifc_only,
        "revit_only": revit_only,
        "ifc_by_level": ifc_by_level,
        "revit_by_level": revit_by_level
    }

def filter_spaces_by_level(spaces_data, target_level):
    """
    Filter spaces data by a specific level
    """
    filtered_data = []
    for space_dict in spaces_data:
        for guid, data in space_dict.items():
            if data["Level Reference"] == target_level:
                filtered_data.append({guid: data})
    return filtered_data

def validate_space_processing(ifc_spaces, spaces, ifc_spaces_data, spaces_data):
    """
    Validate that all spaces are properly processed and counted
    """
    print("=== SPACE PROCESSING VALIDATION ===")
    
    # Count raw elements
    raw_ifc_count = len(ifc_spaces) if ifc_spaces else 0
    raw_revit_count = len(spaces) if spaces else 0
    
    # Count processed data
    processed_ifc_count = len(ifc_spaces_data) if ifc_spaces_data else 0
    processed_revit_count = len(spaces_data) if spaces_data else 0
    
    # Count unique GUIDs in processed data
    ifc_guids = set()
    revit_guids = set()
    
    for ifc_space_dict in ifc_spaces_data:
        for guid in ifc_space_dict.keys():
            ifc_guids.add(guid)
    
    for revit_space_dict in spaces_data:
        for guid in revit_space_dict.keys():
            revit_guids.add(guid)
    
    unique_ifc_count = len(ifc_guids)
    unique_revit_count = len(revit_guids)
    
    print("Raw Element Counts:")
    print("  IFC Elements from Link: {}".format(raw_ifc_count))
    print("  Revit Spaces from Model: {}".format(raw_revit_count))
    print()
    print("Processed Data Counts:")
    print("  IFC Data Objects: {}".format(processed_ifc_count))
    print("  Revit Data Objects: {}".format(processed_revit_count))
    print()
    print("Unique GUID Counts:")
    print("  Unique IFC GUIDs: {}".format(unique_ifc_count))
    print("  Unique Revit GUIDs: {}".format(unique_revit_count))
    print()
    
    # Check for processing issues
    if raw_ifc_count != processed_ifc_count:
        print("WARNING: IFC processing issue - {} elements but {} processed".format(raw_ifc_count, processed_ifc_count))
    
    if raw_revit_count != processed_revit_count:
        print("WARNING: Revit processing issue - {} elements but {} processed".format(raw_revit_count, processed_revit_count))
    
    if processed_ifc_count != unique_ifc_count:
        print("WARNING: Duplicate IFC GUIDs detected")
    
    if processed_revit_count != unique_revit_count:
        print("WARNING: Duplicate Revit GUIDs detected")
    
    # Check for spaces without GUIDs
    ifc_no_guid = 0
    revit_no_guid = 0
    
    for i, space in enumerate(ifc_spaces):
        try:
            guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
            if not guid or guid.strip() == "":
                ifc_no_guid += 1
        except:
            ifc_no_guid += 1
    
    for i, space in enumerate(spaces):
        try:
            guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
            if not guid or guid.strip() == "":
                revit_no_guid += 1
        except:
            revit_no_guid += 1
    
    if ifc_no_guid > 0:
        print("WARNING: {} IFC spaces have no GUID".format(ifc_no_guid))
    
    if revit_no_guid > 0:
        print("WARNING: {} Revit spaces have no GUID".format(revit_no_guid))
    
    print("=" * 50)
    return {
        "raw_ifc_count": raw_ifc_count,
        "raw_revit_count": raw_revit_count,
        "unique_ifc_count": unique_ifc_count,
        "unique_revit_count": unique_revit_count,
        "ifc_no_guid": ifc_no_guid,
        "revit_no_guid": revit_no_guid
    }

def analyze_all_levels(ifc_spaces_data, spaces_data):
    """
    Analyze spaces for all levels in detail
    """
    # Flatten data structures
    ifc_spaces_dict = {}
    revit_spaces_dict = {}
    
    for ifc_space_dict in ifc_spaces_data:
        for guid, data in ifc_space_dict.items():
            ifc_spaces_dict[guid] = data
    
    for revit_space_dict in spaces_data:
        for guid, data in revit_space_dict.items():
            revit_spaces_dict[guid] = data
    
    # Group by levels
    ifc_by_level = {}
    revit_by_level = {}
    
    for guid, data in ifc_spaces_dict.items():
        level = data["Level Reference"]
        if level not in ifc_by_level:
            ifc_by_level[level] = {}
        ifc_by_level[level][guid] = data
    
    for guid, data in revit_spaces_dict.items():
        level = data["Level Reference"]
        if level not in revit_by_level:
            revit_by_level[level] = {}
        revit_by_level[level][guid] = data
    
    # Get all levels
    all_levels = set(list(ifc_by_level.keys()) + list(revit_by_level.keys()))
    
    print("=== DETAILED LEVEL ANALYSIS ===")
    print("Total Levels Found: {}".format(len(all_levels)))
    print()
    
    level_analysis = {}
    
    for level in sorted(all_levels):
        ifc_level_spaces = ifc_by_level.get(level, {})
        revit_level_spaces = revit_by_level.get(level, {})
        
        # Count spaces
        ifc_count = len(ifc_level_spaces)
        revit_count = len(revit_level_spaces)
        
        # Find matches and mismatches
        perfect_matches = 0
        guid_matches = 0
        ifc_only = []
        revit_only = []
        data_mismatches = []
        
        # Check IFC spaces
        for guid, ifc_data in ifc_level_spaces.items():
            if guid in revit_level_spaces:
                revit_data = revit_level_spaces[guid]
                guid_matches += 1
                
                # Check if all data matches
                name_match = ifc_data["Space Name"] == revit_data["Space Name"]
                number_match = ifc_data["Space Number"] == revit_data["Space Number"] 
                level_match = ifc_data["Level Reference"] == revit_data["Level Reference"]
                area_match = ifc_data["Area"] == revit_data["Area"]
                
                if name_match and number_match and level_match and area_match:
                    perfect_matches += 1
                else:
                    data_mismatches.append({
                        "GUID": guid,
                        "IFC_Name": ifc_data["Space Name"],
                        "RVT_Name": revit_data["Space Name"],
                        "IFC_Number": ifc_data["Space Number"],
                        "RVT_Number": revit_data["Space Number"],
                        "Name_Match": name_match,
                        "Number_Match": number_match,
                        "Level_Match": level_match,
                        "Area_Match": area_match
                    })
            else:
                ifc_only.append({
                    "GUID": guid,
                    "Name": ifc_data["Space Name"],
                    "Number": ifc_data["Space Number"]
                })
        
        # Check for Revit-only spaces
        for guid, revit_data in revit_level_spaces.items():
            if guid not in ifc_level_spaces:
                revit_only.append({
                    "GUID": guid,
                    "Name": revit_data["Space Name"],
                    "Number": revit_data["Space Number"]
                })
        
        # Calculate compliance percentage
        if ifc_count > 0:
            compliance_percentage = (perfect_matches / float(ifc_count)) * 100
        else:
            compliance_percentage = 0 if revit_count == 0 else 0  # No IFC spaces to match
        
        # Store analysis
        level_analysis[level] = {
            "ifc_count": ifc_count,
            "revit_count": revit_count,
            "perfect_matches": perfect_matches,
            "guid_matches": guid_matches,
            "ifc_only": ifc_only,
            "revit_only": revit_only,
            "data_mismatches": data_mismatches,
            "compliance_percentage": compliance_percentage
        }
        
        # Print level summary
        print("LEVEL: {}".format(level))
        print("  IFC Spaces: {}".format(ifc_count))
        print("  Revit Spaces: {}".format(revit_count))
        print("  Perfect Matches: {}".format(perfect_matches))
        print("  GUID Matches (data diff): {}".format(guid_matches - perfect_matches))
        print("  Missing in Revit: {}".format(len(ifc_only)))
        print("  Extra in Revit: {}".format(len(revit_only)))
        print("  Compliance: {:.1f}%".format(compliance_percentage))
        
        if len(ifc_only) > 0:
            print("  -> IFC spaces NOT in Revit: {}".format(len(ifc_only)))
        if len(revit_only) > 0:
            print("  -> Revit spaces NOT in IFC: {}".format(len(revit_only)))
        if len(data_mismatches) > 0:
            print("  -> Data mismatches: {}".format(len(data_mismatches)))
        
        print()
    
    return level_analysis

def main():
    # query data
    link_doc = get_link_data(doc)
    ifc_spaces = get_generic_spaces(link_doc)
    spaces = DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_MEPSpaces).WhereElementIsNotElementType().ToElements()

    # process data
    ifc_spaces_data = [get_ifcSpace_data(space) for space in ifc_spaces]
    spaces_data = [get_space_data(space) for space in spaces]
    
    # Validate space processing
    validation_results = validate_space_processing(ifc_spaces, spaces, ifc_spaces_data, spaces_data)
    
    print("=== FULL PROJECT DEBUG ===")
    debug_results = debug_space_matching(ifc_spaces_data, spaces_data)
    
    # Analyze all levels in detail
    print("\n" + "=" * 60)
    level_analysis = analyze_all_levels(ifc_spaces_data, spaces_data)
    
    # Overall compliance summary
    total_ifc = sum([analysis["ifc_count"] for analysis in level_analysis.values()])
    total_revit = sum([analysis["revit_count"] for analysis in level_analysis.values()])
    total_perfect = sum([analysis["perfect_matches"] for analysis in level_analysis.values()])
    total_missing_in_revit = sum([len(analysis["ifc_only"]) for analysis in level_analysis.values()])
    total_extra_in_revit = sum([len(analysis["revit_only"]) for analysis in level_analysis.values()])
    
    overall_compliance = (total_perfect / float(total_ifc)) * 100 if total_ifc > 0 else 0
    
    print("=== OVERALL COMPLIANCE SUMMARY ===")
    print("Total IFC Spaces: {}".format(total_ifc))
    print("Total Revit Spaces: {}".format(total_revit))
    print("Perfect Matches: {}".format(total_perfect))
    print("Missing in Revit: {}".format(total_missing_in_revit))
    print("Extra in Revit: {}".format(total_extra_in_revit))
    print("Overall Compliance: {:.1f}%".format(overall_compliance))
    print()
    
    if total_missing_in_revit > 0:
        print("ACTION REQUIRED: {} IFC spaces need to be placed in Revit".format(total_missing_in_revit))
    if total_extra_in_revit > 0:
        print("REVIEW REQUIRED: {} Revit spaces have no IFC counterpart".format(total_extra_in_revit))
    
    comparison_data = compare_spaces(ifc_spaces_data, spaces_data)

    # Format data for Revit Spaces sheet
    revit_spaces_sheet_data = []
    for space_dict in spaces_data:
        for guid, data in space_dict.items():
            row_data = [
                ("Space Name", data["Space Name"]),
                ("Space Number", data["Space Number"]),
                ("GUID", data["Guid"]),
                ("Level Reference", data["Level Reference"]),
                ("Area", data["Area"]),
                ("Status", data["status"])
            ]
            revit_spaces_sheet_data.append(create_ordered_row(row_data))
    
    # Format data for IFC Spaces sheet
    ifc_spaces_sheet_data = []
    for ifc_dict in ifc_spaces_data:
        for guid, data in ifc_dict.items():
            row_data = [
                ("Room Name", data["Space Name"]),
                ("Room Number", data["Space Number"]),
                ("GUID", data["RGuid"]),
                ("Level Reference", data["Level Reference"]),
                ("Area", data["Area"])
            ]
            ifc_spaces_sheet_data.append(create_ordered_row(row_data))
    
    # Create level-by-level analysis sheets
    level_summary_data = []
    for level, analysis in level_analysis.items():
        row_data = [
            ("Level", level),
            ("IFC_Count", analysis["ifc_count"]),
            ("Revit_Count", analysis["revit_count"]),
            ("Perfect_Matches", analysis["perfect_matches"]),
            ("Missing_in_Revit", len(analysis["ifc_only"])),
            ("Extra_in_Revit", len(analysis["revit_only"])),
            ("Data_Mismatches", len(analysis["data_mismatches"])),
            ("Compliance_Percentage", round(analysis["compliance_percentage"], 1))
        ]
        level_summary_data.append(create_ordered_row(row_data))
    
    # Add debug sheets with ordered columns
    debug_missing_ifc = []
    for item in debug_results["ifc_only"]:
        row_data = [
            ("GUID", item["GUID"]),
            ("Name", item["Name"]),
            ("Number", item["Number"]),
            ("Level", item["Level"]),
            ("Area", item["Area"])
        ]
        debug_missing_ifc.append(create_ordered_row(row_data))
    
    debug_missing_revit = []
    for item in debug_results["revit_only"]:
        row_data = [
            ("GUID", item["GUID"]),
            ("Name", item["Name"]),
            ("Number", item["Number"]),
            ("Level", item["Level"]),
            ("Area", item["Area"])
        ]
        debug_missing_revit.append(create_ordered_row(row_data))
    
    debug_mismatched = []
    for item in debug_results["mismatched_data"]:
        row_data = [
            ("GUID", item["GUID"]),
            ("IFC_Name", item["IFC_Name"]),
            ("RVT_Name", item["RVT_Name"]),
            ("IFC_Number", item["IFC_Number"]),
            ("RVT_Number", item["RVT_Number"]),
            ("IFC_Level", item["IFC_Level"]),
            ("RVT_Level", item["RVT_Level"]),
            ("IFC_Area", item["IFC_Area"]),
            ("RVT_Area", item["RVT_Area"]),
            ("Mismatch_Summary", item["Mismatch_Summary"]),
            ("Name_Match", item["Name_Match"]),
            ("Number_Match", item["Number_Match"]),
            ("Level_Match", item["Level_Match"]),
            ("Area_Match", item["Area_Match"])
        ]
        debug_mismatched.append(create_ordered_row(row_data))
    
    # Convert to .NET Dictionary for MiniExcel
    exported_data = Dictionary[str, object]()
    exported_data["Level Summary"] = level_summary_data
    exported_data["Comparison"] = comparison_data
    exported_data["Revit Spaces"] = revit_spaces_sheet_data
    exported_data["IFC Spaces"] = ifc_spaces_sheet_data
    exported_data["Missing in Revit"] = debug_missing_ifc
    exported_data["Missing in IFC"] = debug_missing_revit
    exported_data["Data Mismatches"] = debug_mismatched
    
    # Save data to Excel
    export_path = save_excel_file("Save Spaces Debug Data")
    if export_path:
        # clean file befor save
        if os.path.exists(export_path):
            os.remove(export_path)
        # Simply save the data without any styling
        MiniExcel.SaveAs(export_path, exported_data)

        # ask user to open the folder
        open_folder = alert("Data exported successfully to:\n{}\n\nOpen folder?".format(export_path), yes=True, no=True)
        if open_folder:
            folder_path = os.path.dirname(export_path)
            os.startfile(folder_path)
        
main()