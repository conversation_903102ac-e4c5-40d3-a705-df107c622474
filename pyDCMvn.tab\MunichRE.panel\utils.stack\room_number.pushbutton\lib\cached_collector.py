import clr
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import System, List
from DCMvn.forms.wpfforms import Select<PERSON>romList
from DCMvn.forms import alert

from user_input import select_categories, select_parameter, ParamDef
from space_props import GenericSpaceProperties

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class CachedCollector:
    """
    CachedCollector class to store and manage cached elements in Revit.
    This class collects various Revit elements and stores them in dictionaries
    for quick access.
    """

    def __init__(self):
        self.__link_transform, self.__link_generic_space_doc = self._collect_linked_space()
        self.__linked_generic_spaces = self._collect_linked_generic_spaces()
        self.__current_model_elements = self._collect_current_model_elements()
        self.__target_parameter = self._collect_target_parameter()
        self.__source_parameter = self._collect_source_parameter()
    
    @property
    def link_transform(self):
        return self.__link_transform
    
    @property
    def link_generic_space_doc(self):
        return self.__link_generic_space_doc
    
    @property
    def linked_generic_spaces(self):
        """
        Get the linked generic spaces from the cached collector.
        
        Returns:
            list: A list of linked generic spaces.
        """
        return self.__linked_generic_spaces
    
    @property
    def current_model_elements(self):
        """
        Get the current model elements from the cached collector.
        
        Returns:
            list: A list of elements from the current Revit model.
        """
        return self.__current_model_elements
    
    @property
    def target_parameter(self):
        # type: () -> ParamDef
        """
        Get the target parameters from the cached collector.
        
        Returns:
            ParamDef: The selected target parameter
        """
        return self.__target_parameter
    
    @property
    def source_parameter(self):
        # type: () -> ParamDef
        """
        Get the source parameter from the cached collector.
        
        Returns:
            ParamDef: The selected source parameter
        """
        return self.__source_parameter
    
    
    def _collect_linked_generic_spaces(self):
        """
        Collect linked generic space from the Revit document.
        
        Returns:
            tuple: A tuple containing the linked generic space, its transform, and the linked document.
        """
        return DB.FilteredElementCollector(self.__link_generic_space_doc) \
            .OfCategory(DB.BuiltInCategory.OST_GenericModel) \
            .WhereElementIsNotElementType() \
            .Where(lambda x: x.get_Parameter(GenericSpaceProperties.ExportToIfcAs).AsString() == GenericSpaceProperties.IfcSpaceType) \
            .ToList()

    def _collect_linked_space(self):
        link_generic_spaces = DB.FilteredElementCollector(HOST_APP.doc) \
                                .OfClass(DB.RevitLinkInstance) \
                                .WhereElementIsNotElementType() \
                                .Where(lambda x: DB.RevitLinkType.IsLoaded(HOST_APP.doc, x.GetTypeId())) \
                                .ToList()
                
        link_generic_space = SelectFromList.show(link_generic_spaces, title="Select Linked ARC", name_attr="Name", multiselect=False)
        if not link_generic_space:
            alert("No linked selected", exitscript=True, warn_icon=True)
        link_transform = link_generic_space.GetTotalTransform() if link_generic_space else None
        link_generic_space_doc = link_generic_space.GetLinkDocument() if link_generic_space else None

        return link_transform, link_generic_space_doc
    
    def _collect_current_model_elements(self):
        """
        Collect elements from the current Revit model.
        
        Returns:
            list: A list of elements from the current model.
        """
        categories = select_categories(HOST_APP.doc)
        if not categories:
            alert("No categories selected.", exitscript=True)
        
        cateFilter = DB.ElementMulticategoryFilter(List[DB.ElementId](categories.Select(lambda x: x.Id).ToList()))
        return DB.FilteredElementCollector(HOST_APP.doc) \
            .WhereElementIsNotElementType() \
            .WherePasses(cateFilter) \
            .ToElements()
            
    
    def _collect_target_parameter(self):
        """
        Collect target parameters from the given elements.
        
        Args:
            elements (list): A list of Revit elements.
        
        Returns:
            list: A list of parameter definitions that are applicable to the elements.
        """
        param_defs = select_parameter(self.__current_model_elements, "Select Target Parameter")
        if not param_defs:
            alert("No applicable parameters found in the current model.", exitscript=True)
        
        return param_defs
    
    
    def _collect_source_parameter(self):
        """
        Collect the source parameter from the target parameters.
        
        Returns:
            ParamDef: The first target parameter if available, otherwise None.
        """
        param_defs = select_parameter(self.__linked_generic_spaces, "Select Source Parameter")
        if not param_defs:
            alert("No applicable parameters found in the linked model.", exitscript=True)
        
        return param_defs

# Create a singleton instance of CachedCollector
HOST_COLLECTOR = CachedCollector()