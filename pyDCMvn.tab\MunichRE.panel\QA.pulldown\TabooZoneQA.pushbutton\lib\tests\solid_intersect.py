from DCMvn.revit.selection import DSelection
from DCMvn.core import DB, UI, DYNAMO, RevitExceptions
from DCMvn.revit.geometry import GetElementMergedSolid, GetPointsFromGeometryObject, compute_oriented_bounding_box_solid

from ..filter import get_condition

def test_solid_intersection(uidoc):
    # type: (UI.UIDocument) -> tuple[DB.Solid, DB.Solid, DB.Solid]
    ele1 = DSelection(uidoc, is_linked=True).pick()
    ele2 = DSelection(uidoc, is_linked=False).pick()

    _, link_instances = get_condition(uidoc.Document, True)
    transform = link_instances[0].GetTotalTransform()
    solid1 = DB.SolidUtils.CreateTransformed(GetElementMergedSolid(ele1), transform)
    solid2 = GetElementMergedSolid(ele2)
    try:
        intersect_solid = DB.BooleanOperationsUtils.ExecuteBooleanOperation(solid1, solid2, DB.BooleanOperationsType.Intersect)
    except RevitExceptions.InvalidOperationException:
        print("Failed to intersect solids, trying to use oriented bounding box")
        if DYNAMO:
            intersect_solid = solid1.Intersect(solid2)
        else:
            points1 = GetPointsFromGeometryObject(solid1)
            points2 = GetPointsFromGeometryObject(solid2)
            oriented_bbox_solid1 = compute_oriented_bounding_box_solid(points1)
            oriented_bbox_solid2 = compute_oriented_bounding_box_solid(points2)
            intersect_solid = DB.BooleanOperationsUtils.ExecuteBooleanOperation(oriented_bbox_solid1, 
                                                                                oriented_bbox_solid2, DB.BooleanOperationsType.Intersect)
    
    print("{} volumne {}".format(ele1.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(), solid1.Volume))
    print("{} volumne {}".format(ele2.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(), solid2.Volume))
    print(intersect_solid.Volume)

    if DYNAMO:
        return intersect_solid.ToProtoType(), solid1.ToProtoType(), solid2.ToProtoType()
    else:
        return intersect_solid, solid1, solid2
