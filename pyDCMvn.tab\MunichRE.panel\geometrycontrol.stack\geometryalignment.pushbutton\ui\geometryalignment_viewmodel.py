# coding: utf-8
import clr
import os
from collections import defaultdict

from pyrevit.output import PyRevitOutputWindow  # noqa

from DCMvn.core import DB
from DCMvn.core.framework import ObservableCollection, List, System
from DCMvn.forms.revit import alert
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.revit.ui import get_mainwindow
from DCMvn.io import pick_folder, get_config_property, set_config_property

from MunichRE.excel_reader import IfcExcelReader
from MunichRE.ifc_entity import IfcEntity
from MunichRE.constants import IGNORE_ALIGNED_EXCEL, EXCEL_EXTENSION, \
    IGNORE_ALIGNED_EXCEL_EXTENSION, GLOBAL_ID, NOT_APPLY

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

separator = "##########################################################################################\n"


class GeometryAlignmentViewModel(ViewModelBase):
    def __init__(self, pyrevit_output, doc, ifc_sheet_name="AllData"):
        # type: (PyRevitOutputWindow, DB.Document, str) -> None
        ViewModelBase.__init__(self)

        # Revit Document
        self._doc = doc
        self._output = pyrevit_output

        # Excel Config
        self._config_section = "pyDCMvn.MunichRE.GeometryAlignment"
        self._excel_folder_path_property = "EXCEL_FOLDER_PATH"
        self._selected_excel_index_property = "SELECTED_EXCEL_INDEX"
        self._excel_folder_path = get_config_property(self._config_section, self._excel_folder_path_property)
        self._selected_excel_index = get_config_property(self._config_section, self._selected_excel_index_property)
        if self._selected_excel_index is None:
            self._selected_excel_index = 0

        self._excelsource = []
        self._ifc_sheet_name = ifc_sheet_name

        # Data collections
        self._ifc_entity = ObservableCollection[System.Object]()
        self._no_ignore_ifc_entity = 0
        self._rvt_model_elements = {}
        self._filtered_ifc_entity = ObservableCollection[System.Object]()
        self._filtered_rvt_model_elements = {}

        # Dictionaries for fast lookups
        self._ifc_entity_dict = {}  # GUID -> IfcEntity
        self._ifc_by_class = {}  # class -> set(GUIDs)
        self._ifc_by_level = {}  # level -> set(GUIDs)
        self._ifc_by_category = {}  # category -> set(GUIDs)
        self._ifc_by_system = {}  # system -> set(GUIDs)

        # Filter options
        self._ifc_classes = []
        self._ifc_level = []
        self._rvt_categories = []
        self._rvt_systems = []

        self._selected_ifc_class_index = 0
        self._selected_ifc_level_index = 0
        self._selected_rvt_category_index = 0
        self._selected_rvt_system_index = 0

        # Input options
        self._guids_input = ""
        self._is_guids_input_selected = False

        # Counters
        self._no_ifc_elements = 0
        self._no_rvt_elements = 0
        self._no_filtered_ifc_elements = 0
        self._no_filtered_rvt_elements = 0

        # Conditions
        self._active_view_only = False
        self._allowable_offset = 10.0
        self._include_not_found = True

        # Command
        self._run_command = RelayCommand(self.execute_run_command, self.can_execute_run_command)
        self._pick_excel_folder_command = RelayCommand(self.execute_pick_excel_folder_command,
                                                       self.can_execute_pick_excel_folder_command)

        # Load data only once
        if self._excel_folder_path and os.path.exists(self._excel_folder_path):
            self.load_excel_folder()

    # region ExcelSource

    @property
    def ExcelSource(self):
        return self._excelsource

    @ExcelSource.setter
    def ExcelSource(self, value):
        self._excelsource = value
        self.RaisePropertyChanged("ExcelSource")

    @property
    def ExcelFolderPath(self):
        return self._excel_folder_path

    @ExcelFolderPath.setter
    def ExcelFolderPath(self, value):
        self._excel_folder_path = value
        self.RaisePropertyChanged("ExcelFolderPath")

    @property
    def SelectedExcelIndex(self):
        return self._selected_excel_index

    @SelectedExcelIndex.setter
    def SelectedExcelIndex(self, value):
        if value < 0 or (self._excelsource and value >= len(self._excelsource)):
            # ignore invalid index
            return
        self._selected_excel_index = value
        self.RaisePropertyChanged("SelectedExcelIndex")
        set_config_property(self._config_section, self._selected_excel_index_property, value)
        self.load_ifc_data()

    # endregion

    # region Total Counts

    @property
    def NoIfcElements(self):
        return len(self._ifc_entity)

    @property
    def NoIgnoreElements(self):
        return self._no_ignore_ifc_entity

    @property
    def NoRvtElements(self):
        return len(self._rvt_model_elements.keys())

    @property
    def IsIFCExcelValid(self):
        return self.NoIfcElements > 0

    # endregion

    # region Filtered Options
    # IFCClass
    @property
    def IFCClass(self):
        return self._ifc_classes

    @IFCClass.setter
    def IFCClass(self, value):
        self._ifc_classes = value
        self.RaisePropertyChanged("IFCClass")

    @property
    def SelectedIFCClassIndex(self):
        return self._selected_ifc_class_index

    @SelectedIFCClassIndex.setter
    def SelectedIFCClassIndex(self, value):
        self._selected_ifc_class_index = value
        self.RaisePropertyChanged("SelectedIFCClassIndex")
        self.update_filtered_counts()

    # IFCLevel
    @property
    def IFCLevel(self):
        return self._ifc_level

    @IFCLevel.setter
    def IFCLevel(self, value):
        self._ifc_level = value
        self.RaisePropertyChanged("IFCLevel")

    @property
    def SelectedIFCLevelIndex(self):
        return self._selected_ifc_level_index

    @SelectedIFCLevelIndex.setter
    def SelectedIFCLevelIndex(self, value):
        self._selected_ifc_level_index = value
        self.RaisePropertyChanged("SelectedIFCLevelIndex")
        self.update_filtered_counts()

    # RvtCategory
    @property
    def RvtCategory(self):
        return self._rvt_categories

    @RvtCategory.setter
    def RvtCategory(self, value):
        self._rvt_categories = value
        self.RaisePropertyChanged("RvtCategory")

    @property
    def SelectedRvtCategoryIndex(self):
        return self._selected_rvt_category_index

    @SelectedRvtCategoryIndex.setter
    def SelectedRvtCategoryIndex(self, value):
        self._selected_rvt_category_index = value
        self.RaisePropertyChanged("SelectedRvtCategoryIndex")
        self.update_filtered_counts()

    # RvtSystem
    @property
    def RvtSystem(self):
        return self._rvt_systems

    @RvtSystem.setter
    def RvtSystem(self, value):
        self._rvt_systems = value
        self.RaisePropertyChanged("RvtSystem")

    @property
    def SelectedRvtSystemIndex(self):
        return self._selected_rvt_system_index

    @SelectedRvtSystemIndex.setter
    def SelectedRvtSystemIndex(self, value):
        self._selected_rvt_system_index = value
        self.RaisePropertyChanged("SelectedRvtSystemIndex")
        self.update_filtered_counts()

    # endregion

    # region GuidsInput Options

    @property
    def IsGuidInputSelected(self):
        return self._is_guids_input_selected

    @IsGuidInputSelected.setter
    def IsGuidInputSelected(self, value):
        self._is_guids_input_selected = value
        self.RaisePropertyChanged("IsGuidInputSelected")
        self.update_filtered_counts()

    @property
    def GuidsInput(self):
        return self._guids_input

    @GuidsInput.setter
    def GuidsInput(self, value):
        self._guids_input = value
        self.RaisePropertyChanged("GuidsInput")
        self.update_filtered_counts()

    # endregion

    # region Conditions

    @property
    def AllowableOffset(self):
        return self._allowable_offset

    @AllowableOffset.setter
    def AllowableOffset(self, value):
        try:
            self._allowable_offset = float(value)
        except ValueError:
            self._allowable_offset = 10.0
        self.RaisePropertyChanged("AllowableOffset")

    @property
    def IsCheckActiveView(self):
        return self._active_view_only

    @IsCheckActiveView.setter
    def IsCheckActiveView(self, value):
        self._active_view_only = value
        self.RaisePropertyChanged("IsCheckActiveView")
        self.load_rvt_data()
        self.update_filtered_counts()

    # endregion

    # region Filtered Counts
    @property
    def NoFilteredIfcElements(self):
        return len(self._filtered_ifc_entity)

    @NoFilteredIfcElements.setter
    def NoFilteredIfcElements(self, value):
        self._no_filtered_ifc_elements = value
        self.RaisePropertyChanged("NoFilteredIfcElements")

    @property
    def NoFilteredRvtElements(self):
        return len(self._filtered_rvt_model_elements.keys())

    @NoFilteredRvtElements.setter
    def NoFilteredRvtElements(self, value):
        self._no_filtered_rvt_elements = value
        self.RaisePropertyChanged("NoFilteredRvtElements")

    # endregion

    # region Commands

    @property
    def RunCommand(self):
        return self._run_command

    def can_execute_run_command(self, param):  # noqa
        return len(self._filtered_rvt_model_elements) > 0

    def execute_run_command(self, param):  # noqa
        miss_alignments = []
        ifc_missing_data = []
        rvt_bbox_not_found = []
        total_elements = len(self._filtered_rvt_model_elements)
        try:
            for e, d in self._filtered_rvt_model_elements.items():
                bbox = e.get_BoundingBox(None)
                if bbox is None:
                    rvt_bbox_not_found.append((e, d))
                    continue
                bbox_center = (bbox.Min + bbox.Max) / 2
                ifc_center = d.centroid
                if ifc_center is None:
                    ifc_missing_data.append((e, d))
                    continue
                offset = ifc_center - bbox_center
                if offset.GetLength() < DB.UnitUtils.ConvertToInternalUnits(self._allowable_offset,
                                                                            DB.UnitTypeId.Millimeters):
                    continue
                else:
                    miss_alignments.append((e, d, offset))
        except Exception as ex:
            alert("Error: {0}".format(ex))
            return
        total_miss_aligned = len(miss_alignments)
        passed_elements = total_elements - total_miss_aligned

        if total_miss_aligned == 0 and len(ifc_missing_data) == 0 and len(rvt_bbox_not_found) == 0:
            alert("All elements are aligned.")
        else:
            output = self._output
            miss_alignments = miss_alignments.OrderByDescending(lambda x: x[2].GetLength()).ToList()  # noqa
            grouped_miss_alignments = defaultdict(lambda: defaultdict(list))
            miss_alignments_count_per_level = defaultdict(int)

            # Missing IFC Data
            if ifc_missing_data is not None and len(ifc_missing_data) > 0:
                output.print_html(separator + '<strong style="color: red">[Missing IFC Data]:</strong>')
                grouped_miss_data = ifc_missing_data.GroupBy(lambda x: x[1].ifc_class).Select(
                    lambda x: x.ToList()).ToList()
                for group in grouped_miss_data:
                    output.print_html(
                        '<strong>IfcElement: {0}<strong> {1}'.format(group[0][1].ifc_class, separator))
                    for e, d in group:
                        print("{0}".format(output.linkify(e.Id, d.id)))

            # Bounding Box Not Found
            if rvt_bbox_not_found is not None and len(rvt_bbox_not_found) > 0:
                output.print_html(separator + '<strong style="color: red">[Bounding Box Not Found]:</strong>')
                grouped_bbox_not_found = rvt_bbox_not_found.GroupBy(lambda x: x[1].ifc_class).Select(
                    lambda x: x.ToList()).ToList()
                for group in grouped_bbox_not_found:
                    output.print_html(
                        '<strong>IfcElement: {0}<strong> {1}'.format(group[0][1].ifc_class, separator))
                    for e, d in group:
                        print("{0}".format(output.linkify(e.Id, d.id)))

            # Mis-Alignments
            if miss_alignments is not None and len(miss_alignments) > 0:
                output.print_html(separator + '<strong style="color: red">[Mis-Aligned]:</strong>')
                for e, d, offset in miss_alignments:
                    grouped_miss_alignments[d.ifc_level][d.ifc_class].append((e, d, offset))
                    miss_alignments_count_per_level[d.ifc_level] += 1

                for ifc_level in sorted(grouped_miss_alignments.keys()):
                    class_group = grouped_miss_alignments[ifc_level]
                    output.print_html('<strong style="color: blue">IfcStorey: {0}</strong> {1}'.format(ifc_level, separator))
                    for ifc_class, elements in class_group.items():
                        output.print_html(
                            '<strong>IfcElement: {0}</strong> {1}'.format(ifc_class,separator))
                        for e, d, offset in elements:
                            offset_mm = DB.UnitUtils.ConvertFromInternalUnits(
                                offset.GetLength(), DB.UnitTypeId.Millimeters
                            )
                            print(
                                "{0}, Offset: {1} mm".format(
                                    output.linkify(e.Id, d.id), offset_mm
                                )
                            )

                output.print_html(separator + '<strong style>Summary Report:</strong>')
                output.print_html('Total Passed Elements: <strong style="color: green">{0}</strong> out of <strong style>{1}</strong>. There are <strong style="color: red">{2}</strong> MisAligned Elements'.format(passed_elements,total_elements,total_miss_aligned))
                print("MisAligned Elements per Storey:")
                for level in sorted(miss_alignments_count_per_level.keys()):
                    output.print_html(' - Storey {}: <strong style="color: red">{}</strong>'.format(level,miss_alignments_count_per_level[level]))

    @property
    def PickExcelFolderCommand(self):
        return self._pick_excel_folder_command

    def can_execute_pick_excel_folder_command(self, param):  # noqa
        return True

    def execute_pick_excel_folder_command(self, param):  # noqa
        revit_main_window = get_mainwindow()
        folder = pick_folder("Select IFC Excel Folder", owner=revit_main_window)
        if folder and os.path.exists(folder):
            set_config_property(self._config_section, self._excel_folder_path_property, folder)
            self.ExcelFolderPath = folder
            self.load_excel_folder()
        else:
            alert("No folder selected.")
            self.clear_data()
            self.clear_filtered_data()

    # endregion

    # region Data Loading

    def clear_data(self):
        self._ifc_entity.Clear()
        self._rvt_model_elements.clear()

    def clear_filtered_data(self):
        self._filtered_ifc_entity.Clear()
        self._filtered_rvt_model_elements.clear()

    def load_excel_folder(self):
        self._excelsource = [f for f in os.listdir(self._excel_folder_path) if
                             f.endswith(EXCEL_EXTENSION) and not (f.startswith('~') or IGNORE_ALIGNED_EXCEL in f)]
        self.RaisePropertyChanged("ExcelSource")
        # Only load if there's a valid index
        if self._excelsource and 0 <= self._selected_excel_index < len(self._excelsource):
            self.load_ifc_data()

    def load_rvt_data(self):
        self._rvt_model_elements.clear()
        self._filtered_rvt_model_elements.clear()

        # Load Revit elements once
        categories = self._doc.Settings.Categories
        model_categories = [c.Id for c in categories if c.CategoryType == DB.CategoryType.Model]
        categories_filter = DB.ElementMulticategoryFilter(List[DB.ElementId](model_categories))

        if self.IsCheckActiveView:
            elements = DB.FilteredElementCollector(self._doc, self._doc.ActiveView.Id).WherePasses(
                categories_filter).WhereElementIsNotElementType()
        else:
            elements = DB.FilteredElementCollector(self._doc).WherePasses(
                categories_filter).WhereElementIsNotElementType()

        def get_ifc_guid(e):
            mark_ = e.get_Parameter(DB.BuiltInParameter.IFC_GUID)
            return mark_.AsString() if mark_ else None

        # Use already built _ifc_entity_dict
        elements_mark = {e: get_ifc_guid(e) for e in elements}
        for e, mark in elements_mark.items():
            if mark in self._ifc_entity_dict:
                self._rvt_model_elements[e] = self._ifc_entity_dict[mark]

        self.RaisePropertyChanged("NoRvtElements")

    def load_ifc_data(self):
        self.clear_data()

        if not self._excelsource or self._selected_excel_index < 0 \
                or self._selected_excel_index >= len(self._excelsource):
            return

        excel_dir = self._excelsource[self._selected_excel_index]
        excel_path = os.path.join(self._excel_folder_path, excel_dir)
        ignore_excel_dir = excel_dir.replace(EXCEL_EXTENSION, IGNORE_ALIGNED_EXCEL_EXTENSION)
        ignore_excel_path = os.path.join(self._excel_folder_path, ignore_excel_dir)

        reader = IfcExcelReader(excel_path, self._ifc_sheet_name)
        data = reader.data

        ignore_id = []
        if os.path.exists(ignore_excel_path):
            ignore_reader = IfcExcelReader(ignore_excel_path, self._ifc_sheet_name)
            ignore_data = ignore_reader.data
            ignore_id = [getattr(d, GLOBAL_ID) for d in ignore_data]
            self._no_ignore_ifc_entity = len(ignore_id)

        # Add data to collection
        try:
            for d in data:
                if ignore_id and getattr(d, GLOBAL_ID) in ignore_id:
                    continue
                entity = IfcEntity(d)
                self._ifc_entity.Add(entity)
        except Exception:
            import traceback
            alert("Error: {0}".format(traceback.format_exc()), warn_icon=True)
            return

        # Build dictionaries for fast lookups
        self.build_ifc_indexes()

        # Add "Not Apply" option at the start
        self.IFCClass = [NOT_APPLY] + sorted(self._ifc_by_class.keys())
        self.IFCLevel = [NOT_APPLY] + sorted(self._ifc_by_level.keys())
        self.RvtCategory = [NOT_APPLY] + sorted(self._ifc_by_category.keys())
        self.RvtSystem = [NOT_APPLY] + sorted(self._ifc_by_system.keys())

        # Reset selections
        self.SelectedIFCClassIndex = 0
        self.SelectedIFCLevelIndex = 0
        self.SelectedRvtCategoryIndex = 0
        self.SelectedRvtSystemIndex = 0

        # load rvt data once
        self.load_rvt_data()

        self.RaisePropertyChanged("NoIfcElements")
        self.RaisePropertyChanged("IsIFCExcelValid")
        self.update_filtered_counts()

    def build_ifc_indexes(self):
        # Clear old indexes
        self._ifc_entity_dict.clear()
        self._ifc_by_class.clear()
        self._ifc_by_level.clear()
        self._ifc_by_category.clear()
        self._ifc_by_system.clear()

        def add_to_index(index_dict, key, guid_):
            if key not in index_dict:
                index_dict[key] = set()
            index_dict[key].add(guid_)

        # Build indexes
        for d in self._ifc_entity:
            guid = d.id
            self._ifc_entity_dict[guid] = d
            if d.ifc_class:
                add_to_index(self._ifc_by_class, d.ifc_class, guid)
            if d.ifc_level:
                add_to_index(self._ifc_by_level, d.ifc_level, guid)
            if d.rvt_category:
                add_to_index(self._ifc_by_category, d.rvt_category, guid)
            if d.rvt_system:
                add_to_index(self._ifc_by_system, d.rvt_system, guid)

    def update_filtered_counts(self):
        self.clear_filtered_data()

        # Use set intersection for filtering
        all_ids = set(self._ifc_entity_dict.keys())

        # Filter by GUID if selected
        if self.IsGuidInputSelected and self.GuidsInput.strip():
            guids = {g.strip() for g in self.GuidsInput.split('\n') if g.strip()}
            filtered_ids = all_ids.intersection(guids)
        else:
            filtered_ids = all_ids

        # Apply IFC filters (# `Not Apply` is index 0)
        if self.IFCClass and self.SelectedIFCClassIndex > 0:  # noqa
            selected_class = self.IFCClass[self.SelectedIFCClassIndex]
            filtered_ids = filtered_ids.intersection(self._ifc_by_class.get(selected_class, set()))

        if self.IFCLevel and self.SelectedIFCLevelIndex > 0:
            selected_level = self.IFCLevel[self.SelectedIFCLevelIndex]
            filtered_ids = filtered_ids.intersection(self._ifc_by_level.get(selected_level, set()))

        if self.RvtCategory and self.SelectedRvtCategoryIndex > 0:  # noqa
            selected_cat = self.RvtCategory[self.SelectedRvtCategoryIndex]
            filtered_ids = filtered_ids.intersection(self._ifc_by_category.get(selected_cat, set()))

        if self.RvtSystem and self.SelectedRvtSystemIndex > 0:
            selected_sys = self.RvtSystem[self.SelectedRvtSystemIndex]
            filtered_ids = filtered_ids.intersection(self._ifc_by_system.get(selected_sys, set()))

        # Now we have a final set of IFC GUIDs after all filters
        for guid in filtered_ids:
            self._filtered_ifc_entity.Add(self._ifc_entity_dict[guid])

        # Filter RVT elements by these IFC entities
        guids_set = set(filtered_ids)
        # Just keep the RVT elements whose mark GUID is in the filtered set
        for e, d in self._rvt_model_elements.items():
            if d.id in guids_set:
                self._filtered_rvt_model_elements[e] = d

        # Update counts at once
        self.RaisePropertyChanged("NoFilteredIfcElements")
        self.RaisePropertyChanged("NoFilteredRvtElements")
        self._run_command.RaiseCanExecuteChanged()

    # endregion
