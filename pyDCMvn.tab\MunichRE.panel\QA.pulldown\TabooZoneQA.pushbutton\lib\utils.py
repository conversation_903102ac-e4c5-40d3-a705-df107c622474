from DCMvn.core import DB
from clashes.arc_curve import ArcCurve

def are_bbox_overlaps(bbox1, bbox2, tolerance=1e-9):
    # type: (DB.BoundingBoxXYZ, DB.BoundingBoxXYZ, float) -> bool

    source_min = bbox1.Transform.OfPoint(bbox1.Min) if not bbox1.Transform.IsIdentity else bbox1.Min
    source_max = bbox1.Transform.OfPoint(bbox1.Max) if not bbox1.Transform.IsIdentity else bbox1.Max
    other_min = bbox2.Transform.OfPoint(bbox2.Min) if not bbox2.Transform.IsIdentity else bbox2.Min
    other_max = bbox2.Transform.OfPoint(bbox2.Max) if not bbox2.Transform.IsIdentity else bbox2.Max

    overlap_x = not (source_max.X < other_min.X - tolerance or source_min.X > other_max.X + tolerance)
    overlap_y = not (source_max.Y < other_min.Y - tolerance or source_min.Y > other_max.Y + tolerance)
    overlap_z = not (source_max.Z < other_min.Z - tolerance or source_min.Z > other_max.Z + tolerance)

    return overlap_x and overlap_y and overlap_z


def get_element_from_ref(reference, current_doc):
    # type: (DB.Reference, DB.Document) -> DB.Element  # noqa
    """
    Get element from DB.Reference
    Args:
        reference (DB.Reference): Revit reference
        current_doc (DB.Document): current document

    Returns:
        DB.Element: Revit element
    """
    element = current_doc.GetElement(reference.ElementId)
    if isinstance(element, DB.RevitLinkInstance):
        return element.GetLinkDocument().GetElement(reference.LinkedElementId)
    return element


def get_workset_by_name(document, workset_name):
    # type: (DB.Document, str) -> DB.Workset
    """
    Get workset by name
    Args:
        document (DB.Document): Revit document
        workset_name (str): workset name

    Returns:
        DB.Workset: workset
    """
    worksets = DB.FilteredWorksetCollector(document).OfKind(DB.WorksetKind.UserWorkset).ToWorksets()
    workset = next((x for x in worksets if x.Name == workset_name), None)
    return workset


def get_arc_from_solid(solid):
    # type: (DB.Solid) -> list[ArcCurve]  # noqa
    """
    Get arc from solid
    Args:
        solid (DB.Solid): solid

    Returns:
        list[ArcCurve]: list of arc
    """
    arcs = []
    for edge in solid.Edges:  # noqa
        curve = edge.AsCurve() # type: DB.Curve
        if isinstance(curve, DB.Arc):
            arcs.append(curve)

    return [ArcCurve(arc) for arc in arcs]



