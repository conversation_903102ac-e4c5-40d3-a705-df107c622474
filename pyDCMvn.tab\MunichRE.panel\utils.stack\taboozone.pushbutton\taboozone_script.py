import clr
import System
from pyrevit import script

from DCMvn.core import DB, HOST_APP, get_output, REVIT_VERSION
from DCMvn.core.framework import List, IO
from DCMvn.io import pick_folder
from DCMvn.forms import wpfforms, alert
from DCMvn.revit.query import get_name
from DCMvn.revit.ui import get_mainwindow
from DCMvn.revit.transaction import transaction_wrapper

from MunichRE.excel_reader import IfcExcelReader, GLOBAL_ID

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()
doc = HOST_APP.doc

def is_physical_element(element):
    # type: (DB.Element) -> bool
    if element.Category is None:
        return False

    if (DB.BuiltInCategory(element.Category.Id.IntegerValue) # noqa
            == DB.BuiltInCategory.OST_HVAC_Zones):
        return False

    return element.Category.CategoryType == DB.CategoryType.Model and element.Category.CanAddSubcategory


def create_ifc_filter(raw_id):
    # type: (str) -> DB.ElementParameterFilter
    guid_provider = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.IFC_GUID))
    if REVIT_VERSION >= 2022:
        filter_rule = DB.FilterStringRule(guid_provider, DB.FilterStringEquals(), raw_id)
    else:
        filter_rule = DB.FilterStringRule(guid_provider, DB.FilterStringEquals(), raw_id, True)  # noqa

    return DB.ElementParameterFilter(DB.FilterInverseRule(filter_rule))


def create_filter_by_element_collector(apply_categories, exist_parameter_elements):
    # type: (List[DB.ElementId], List[DB.ParameterFilterElement]) -> None
    link_docs = (DB.FilteredElementCollector(doc).OfClass(DB.RevitLinkInstance) # noqa
                 .Where(lambda x: DB.RevitLinkType.IsLoaded(doc, x.GetTypeId()))
                 .Select(lambda x: x.GetLinkDocument())
                 .ToList())

    parameter_filter_element_names = [get_name(x) for x in exist_parameter_elements]

    select_docs = wpfforms.SelectFromList.show(context=link_docs, name_attr="Title", title="Select a model",
                                              multiselect=True,
                                              show_min_button=False, show_maxrestore_button=False)
    if not select_docs:
        alert("No model selected", warn_icon=True, exitscript=True)

    for document in select_docs:
        physical_element = (DB.FilteredElementCollector(document).WhereElementIsNotElementType().Where(lambda x: is_physical_element(x)).ToList())  # noqa

        parameter_filters = []
        for element in physical_element:
            ifc_guid = element.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
            if not ifc_guid:
                continue

            filter_rule = create_ifc_filter(ifc_guid)
            parameter_filters.append(filter_rule)

        if not parameter_filters:
            logger.error("No IFC GUID found in {}".format(document.Title))
            continue
        combine_filter = DB.LogicalAndFilter(List[DB.ElementFilter](parameter_filters))
        if document.Title in parameter_filter_element_names:
            parameter_filter_element = next(x for x in exist_parameter_elements if get_name(x) == document.Title)  # type: DB.ParameterFilterElement  # noqa
            if not parameter_filter_element:
                DB.ParameterFilterElement.Create(doc, document.Title, apply_categories,
                                                 combine_filter)
            else:
                parameter_filter_element.ClearRules()
                parameter_filter_element.SetElementFilter(combine_filter)

        else:
            DB.ParameterFilterElement.Create(doc, document.Title, apply_categories,
                                             combine_filter)


def create_filter_by_excel(apply_categories, exist_parameter_elements):
    # type: (List[DB.ElementId], List[DB.ParameterFilterElement]) -> None
    select_folder = pick_folder("Select a folder containing Excel files", owner=get_mainwindow())
    if not select_folder:
        alert("No folder selected", warn_icon=True, exitscript=True)

    excel_files = IO.Directory.GetFiles(select_folder, "*.xlsx")
    if not excel_files:
        alert("No Excel files found", warn_icon=True, exitscript=True)

    is_hg = doc.Title.Contains("HG")
    is_o1 = doc.Title.Contains("O1")
    excel_files = [x for x in excel_files if (is_hg and "HG" in x) or (is_o1 and "O1" in x)]
    parameter_filter_element_names = [get_name(x) for x in exist_parameter_elements]

    for excel_file in excel_files:
        excel_data = IfcExcelReader(excel_file, "Sheet1").data
        try:
            guids = [getattr(x, GLOBAL_ID) for x in excel_data]
        except AttributeError:
            logger.error("No `GlobalId` column found in {}".format(excel_file))
            continue
        filter_rules = [create_ifc_filter(x) for x in guids]

        if not filter_rules:
            logger.error("No IFC GUID found in {}".format(excel_file))
            continue

        combine_filter = DB.LogicalAndFilter(List[DB.ElementFilter](filter_rules))
        excel_condition = "_".join(IO.Path.GetFileNameWithoutExtension(excel_file).split("_")[:-2])
        if excel_condition in parameter_filter_element_names:
            parameter_filter_element = next(x for x in exist_parameter_elements if get_name(x) == excel_condition)  # type: DB.ParameterFilterElement  # noqa
            if not parameter_filter_element:
                DB.ParameterFilterElement.Create(doc, excel_condition, apply_categories, combine_filter)
            else:
                parameter_filter_element.ClearRules()
                parameter_filter_element.SetElementFilter(combine_filter)
        else:
            DB.ParameterFilterElement.Create(doc, excel_condition, apply_categories, combine_filter)


@transaction_wrapper(message="Create inverse filter")
def main():
    parameter_elements = (DB.FilteredElementCollector(doc).  # type: List[DB.ParameterFilterElement] # noqa
                          OfClass(DB.ParameterFilterElement).
                          WhereElementIsNotElementType().
                          ToList())
    apply_categories = List[DB.ElementId]([DB.ElementId(DB.BuiltInCategory.OST_Walls),
                                           DB.ElementId(DB.BuiltInCategory.OST_Floors),
                                           DB.ElementId(DB.BuiltInCategory.OST_Columns),
                                           DB.ElementId(DB.BuiltInCategory.OST_GenericModel),
                                           DB.ElementId(DB.BuiltInCategory.OST_StructuralColumns),
                                           DB.ElementId(DB.BuiltInCategory.OST_StructuralFraming)])

    excel_or_linkdoc = wpfforms.CommandSwitchWindow.show(["Excel", "Link Document"], "Select source")
    if excel_or_linkdoc:
        if excel_or_linkdoc == "Excel":
            create_filter_by_excel(apply_categories, parameter_elements)
        else:
            create_filter_by_element_collector(apply_categories, parameter_elements)

if __name__ == "__main__":
    main()


