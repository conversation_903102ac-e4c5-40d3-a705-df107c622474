# coding: utf-8
"""Align the level of elements to the right level"""
import clr
import System
import os
from System import Type
from System.Collections.Generic import List
from pyrevit import script
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.io import save_excel_file
from MunichRE.excel_reader import MiniExcel

clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()
doc = HOST_APP.doc
UNDERFINED_LEVEL = "Undefined"

def _get_element_level(element):
    try:
        schedule_level = element.get_Parameter(DB.BuiltInParameter.SCHEDULE_LEVEL_PARAM)
        if schedule_level is not None and schedule_level.AsElementId() != DB.ElementId.InvalidElementId:
            return doc.GetElement(schedule_level.AsElementId())
        elif element.LevelId != DB.ElementId.InvalidElementId:
            return doc.GetElement(element.LevelId)
        elif element.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM) is not None:
            return doc.GetElement(element.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM).AsElementId())
        elif element.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM) is not None:
            return doc.GetElement(element.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM).AsElementId())
        elif hasattr(element, 'Level' and element.Level is not None):
            return element.Level
        elif hasattr(element, 'GenLevel') and element.GenLevel is not None:
            return element.GenLevel
        return None
    except Exception as e:
        logger.error("Error getting level for element {}: {}".format(element.Id, str(e)))
        return None
    
def _get_name(item):
    try:
        return item.Name
    except AttributeError:
        return None
    
def _get_element_category(element):
    try:
        return element.Category.Name if element.Category else "Unknown"
    except Exception:
        return "Unknown"
    
    
def _get_level_at_location_height(element, current_transform):
    # type: (DB.Element, DB.Transform) -> DB.Level
    location = element.Location
    if location is None:
        return None
    
    location_point = None
    if isinstance(element.Location, DB.LocationPoint):
        location_point = element.Location.Point
    else:
        location_curve = element.Location.Curve
        sorted_points = sorted(location_curve.Tessellate(), key=lambda x: (x.X, x.Y, x.Z))
        location_point = sorted_points[0]
    
    location_height = current_transform.OfPoint(location_point).Z

    belowOrAtLevels = levels.Where(lambda x: x.Elevation <= location_height).ToList()
    
    if belowOrAtLevels.Count == 0:
        true_level = levels.OrderBy(lambda x: x.Elevation).FirstOrDefault()
    else:
        true_level = belowOrAtLevels.OrderByDescending(lambda x: x.Elevation).FirstOrDefault()
    if true_level is None:
        logger.error("No valid level found for element {} at height {}".format(output.linkify(element.Id), location_height))
        return None
    return true_level

# Current transform
base_point = DB.BasePoint.GetProjectBasePoint(doc).Position  # type: DB.XYZ
current_transform = DB.Transform.CreateTranslation(base_point).Inverse  # type: DB.Transform

# Get all Levels
levels = DB.FilteredElementCollector(doc).OfClass(DB.Level).ToElements().OrderByDescending(
    lambda x: x.Elevation)  # type: List[DB.Level]
levels_dict = {item.Id: item.Elevation for item in levels}

# Get all the elements in the document
class_filter = DB.ElementMulticlassFilter(List[Type]([clr.GetClrType(DB.FamilyInstance), clr.GetClrType(DB.MEPCurve)]))
insulation_filter = DB.ElementMulticategoryFilter(List[DB.BuiltInCategory]([DB.BuiltInCategory.OST_DuctInsulations,
                                                                            DB.BuiltInCategory.OST_PipeInsulations]))
insulations = DB.FilteredElementCollector(doc).WherePasses(
    insulation_filter).WhereElementIsNotElementType().ToElementIds()

if not insulations or len(insulations) == 0:
    elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id).WherePasses(class_filter).ToElements()
else:
    elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id).WherePasses(class_filter).Excluding(
        insulations).ToElements()

total_elements = len(elements)
element_default_level_dict = {item.Id: _get_element_level(item) for item in elements}
element_right_level_dict = {item.Id: _get_level_at_location_height(item, current_transform) for item in elements}

# Check Raw Level and Right Level
not_equal_levels = []
level_null = []
not_equal_levels_count = {}
level_null_count = {}
for elementId, origin_level in element_default_level_dict.items():
    right_level = element_right_level_dict.get(elementId)
    right_level_name = right_level.Name if right_level else UNDERFINED_LEVEL

    if right_level is None and origin_level is None:
        level_null.append(elementId)
        level_null_count[UNDERFINED_LEVEL] = level_null_count.get(UNDERFINED_LEVEL, 0) + 1
    if right_level is None or origin_level is None:
        not_equal_levels.append((elementId, origin_level, right_level))
        not_equal_levels_count[right_level_name] = not_equal_levels_count.get(right_level_name, 0) + 1
    elif right_level.Name != origin_level.Name:
        not_equal_levels.append((elementId, origin_level, right_level))
        not_equal_levels_count[right_level_name] = not_equal_levels_count.get(right_level_name, 0) + 1
        


# Fix the levels
grouped_elements = {}
with DB.Transaction(doc, "Correct Levels") as t:
    t.Start()
    for elementId, origin_level, true_level in not_equal_levels:
        
        element = doc.GetElement(elementId)
        category_name = _get_element_category(element)
        element_guid = element.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString() if element.get_Parameter(DB.BuiltInParameter.IFC_GUID) else "N/A"
        can_set_level = True
        
        if __shiftclick__: # noqa # type: ignore
            if isinstance(element, DB.MEPCurve):
                element.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM).Set(true_level.Id)
            elif isinstance(element, DB.FamilyInstance):
                location = element.Location.Point.Z

                try:
                    schedule_level = element.get_Parameter(DB.BuiltInParameter.INSTANCE_SCHEDULE_ONLY_LEVEL_PARAM)
                    family_level = element.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM)
                    
                    if schedule_level is not None and schedule_level.AsElementId() != DB.ElementId.InvalidElementId:
                        schedule_level.Set(true_level.Id)
                    elif family_level is not None and family_level.AsElementId() != DB.ElementId.InvalidElementId:
                        family_level.Set(true_level.Id)

                    element.get_Parameter(DB.BuiltInParameter.INSTANCE_ELEVATION_PARAM).Set(location - true_level.Elevation)
                except Exception:
                    can_set_level = False
            
            fix_status = "<span style='color:red;'>Failed</span>" if not can_set_level else "<span style='color:green;'>Success</span>"  
            message = "{} - Original Level: <strong style=\"color: red\">{}</strong> - Fix Status: {}"\
                .format(output.linkify(elementId, element_guid), _get_name(origin_level), fix_status)   
        else:
            message = "{} - Original Level: <strong style=\"color: red\">{}</strong>"\
                                .format(output.linkify(elementId, element_guid), _get_name(origin_level))

        # Grouping by True Level and then Category
        level_name = true_level.Name if true_level is not None else UNDERFINED_LEVEL
        origin_level_name = origin_level.Name if origin_level is not None else UNDERFINED_LEVEL
        
        if level_name not in grouped_elements:
            grouped_elements[level_name] = {}
        if category_name not in grouped_elements[level_name]:
            grouped_elements[level_name][category_name] = []

        grouped_elements[level_name][category_name].append(
            (message, element_guid, origin_level_name)
        )
    t.Commit()

def save_wrong_levels_report(grouped_elements):
    """
    Generate an Excel report for elements with wrong levels using MiniExcel.
    
    Args:
        grouped_elements: Dictionary structure containing elements with wrong levels,
                          organized by correct level and category
    """
    try:
        file_path = save_excel_file(title="Save Wrong Levels Report")
        if not file_path:
            output.print_md('# Report not saved')
            return
        
        report_data = []
        
        for true_level, categories in grouped_elements.items():
            for category, element_infos in categories.items():
                for element_info in element_infos:
                    guid = element_info[1]
                    origin_level = element_info[2]
                    
                    row_data = OrderedDictionary()
                    row_data["Guid"] = guid
                    row_data["Category"] = category
                    row_data["CurrentLevel"] = origin_level
                    row_data["CorrectLevel"] = true_level
                    row_data["Approver"] = ""
                    row_data["Reason"] = ""
                    report_data.append(row_data)
        if os.path.exists(file_path):
            os.remove(file_path)
        MiniExcel.SaveAs(file_path, report_data)
        
        output.print_md('# Wrong Levels Report saved successfully: {}'.format(file_path))
        return file_path
    except Exception as e:
        output.print_md('# Failed to save Wrong Levels Report: {}'.format(str(e)))
        return None


# Print grouped results
separator = "##########################################################################################\n"
for true_level, categories in grouped_elements.items():
    output.insert_divider()
    output.print_html('<strong style="color: blue">Correct Level: {0}</strong>'.format(true_level))
    for category, elements in categories.items():
        output.print_html('<div style="display: flex; align-items: center; width: 100%;"><strong>Category: {0}</strong><span style="border-bottom: 1px solid #ccc; flex-grow: 1; margin-left: 10px;"></span></div>'.format(category))
        for element_info in elements:
            output.print_html(element_info[0])
output.insert_divider()
output.print_html('<strong style>Summary Report:</strong>')
output.print_html("Total Elements in Model: "+ '<strong style>{}</strong>'.format(total_elements))
output.print_html("Elements with Incorrect Level: " + '<strong style="color: red">{}</strong>'.format(len(not_equal_levels)))
for level, count in sorted(not_equal_levels_count.items()):
    output.print_html('- Level {}: '.format(level) + '<strong style="color: red">{}</strong>'.format(count))
output.print_html("Element with Not Defined Level: " + '<strong style="color: red">{}</strong>'.format(len(level_null)))
for level, count in sorted(level_null_count.items()):
    output.print_html('- Level {}: '.format(level) + '<strong style="color: red">{}</strong>'.format(count))
output.close_others()

# Ask if user wants to save report
if grouped_elements and len(not_equal_levels) > 0:
    from DCMvn.forms import alert
    save_report = alert("Do you want to save a report of elements with wrong levels?", yes=True, no=True)
    if save_report:
        save_wrong_levels_report(grouped_elements)
