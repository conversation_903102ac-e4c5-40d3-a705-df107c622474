from DCMvn.core import DB, HOST_APP
from space_props import SpaceProperties
from generic_space import GenericSpace

class SpatialFactory:
    """
    Factory class for creating spatial space objects.
    """
    
    @staticmethod
    def create_separators(generic_space, create_space=True):
        # type: (GenericSpace, bool) -> DB.UV
        """ Create separators for a given generic space.

        Args:
            generic_space (GenericSpace): The generic space for which to create separators.
            create_space (bool, optional): If True, create space separator. else, create room separator. Defaults to True.

        Returns:
            DB.UV: A UV object representing center points of the separators.
        """
        sketch_plane = DB.SketchPlane.Create(HOST_APP.doc, 
                                             DB.Plane.CreateByNormalAndOrigin(DB.XYZ.BasisZ,
                                                                              generic_space.curve_arrays[0][0].GetEndPoint(0)))
        current_attach_view = generic_space.attached_plan_view
        
        for curve_array in generic_space.curve_arrays:
            if create_space:
                HOST_APP.doc.Create.NewSpaceBoundaryLines(sketch_plane, curve_array, current_attach_view)
            else:
                HOST_APP.doc.Create.NewRoomBoundaryLines(sketch_plane, curve_array, current_attach_view)

    @staticmethod
    def create_space(generic_space, location):
        # type: (GenericSpace, DB.UV) -> DB.Element
        """
        Create a spatial space object based on the specified type.

        Args:
            generic_space (GenericSpace): The generic space to create a space for.
            location (DB.UV): The location where the space will be created.
        """
        return HOST_APP.doc.Create.NewSpace(generic_space.level, DB.UV(location.X, location.Y))


    @staticmethod
    def create_room(generic_space, location):
        # type: (GenericSpace, DB.UV) -> DB.Element
        """
        Create a spatial room object based on the specified type.

        Args:
            generic_space (GenericSpace): The generic space to create a room for.
            location (DB.UV): The location where the room will be created.
        """
        return HOST_APP.doc.Create.NewRoom(generic_space.level, DB.UV(location.X, location.Y))
        
    @staticmethod
    def map_properties(space, generic_space):
        # type: (DB.Element, GenericSpace) -> dict
        """
        Map properties from a space object to a dictionary.

        :param space: The space object to map properties from.
        :return: A dictionary containing the mapped properties.
        """
        
        space.get_Parameter(SpaceProperties.Guid).Set(generic_space.guid)
        space.get_Parameter(SpaceProperties.Name).Set(generic_space.name)
        space.get_Parameter(SpaceProperties.Number).Set(generic_space.number)
        space.get_Parameter(SpaceProperties.BaseOffset).Set(generic_space.base_offset)
        space.get_Parameter(SpaceProperties.LimitOffset).Set(generic_space.limit_offset)