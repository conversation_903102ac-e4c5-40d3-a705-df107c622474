import clr
import System
from pyrevit import DB, HOST_APP
from DCMvn.revit.geometry import are_elements_intersect
from DCMvn.core.framework import Trace, List, System
from user_input import get_parameter

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class IntersectionChecker:
    """
    A class to check if two sets of elements intersect.
    """

    def __init__(self, rooms, mep_elements):
        """ Initialize the IntersectionChecker with two sets of elements.

        Args:
            rooms (list): A list of room elements.
            mep_elements (list): A list of MEP elements.
        """
        self.rooms = rooms
        self.mep_elements = mep_elements
        self.total_rooms = len(rooms)
        self.total_mep_elements = len(mep_elements)
        self.match_set = []

    def check_intersection(self, transform):
        # type: (DB.Transform) -> None
        """ Check for intersections between rooms and MEP elements.

        Returns:
            _type_: _description_
        """
        
        mep_element_ids = List[DB.ElementId]([i.Id for i in self.mep_elements])
        for room in self.rooms:
            origin_bbox = room.get_BoundingBox(None)

            min_point = transform.OfPoint(origin_bbox.Min)
            max_point = transform.OfPoint(origin_bbox.Max)

            outline = DB.Outline(min_point, max_point)
            bbox_filter = DB.BoundingBoxIntersectsFilter(outline)  # type: DB.BoundingBoxIntersectsFilter
            
            mep_elements_in_bbox = DB.FilteredElementCollector(HOST_APP.doc, mep_element_ids) \
                .WhereElementIsNotElementType() \
                .WherePasses(bbox_filter) \
                .ToElements()
            print("Checking intersections for room: {0} with {1} MEP elements in bounding box.".format(room.Id, len(mep_elements_in_bbox)))
            for mep_element in mep_elements_in_bbox:
                try:
                    if are_elements_intersect(room, mep_element, largest_solid_only=True):
                        self.match_set.append((room, mep_element))
                except Exception as e:
                    print("Error checking intersection for room {0} and MEP element {1}: {2}".format(room.Id, mep_element.Id, str(e)))
                
                
    def set_parameters(self, source_param, target_param):
        """Set the source and target parameters for the intersection check.

        Args:
            source_param (ParamDef): The parameter definition for the source.
            target_param (ParamDef): The parameter definition for the target.
        """
        
        for room, mep_element in self.match_set:
            if source_param and target_param:
                source = get_parameter(source_param, room)
                target = get_parameter(target_param, mep_element)
                target.Set(source.AsString())
            else:
                print("Parameters not found for {} or {}".format(mep_element.Id, room.Id))