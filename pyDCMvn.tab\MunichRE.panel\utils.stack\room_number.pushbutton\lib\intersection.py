import clr
import System
from pyrevit import DB, HOST_APP
from DCMvn.revit.geometry import are_elements_intersect
from DCMvn.core.framework import List
from user_input import get_parameter

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class IntersectionChecker:
    """
    A class to check if two sets of elements intersect and track comprehensive statistics.
    """

    def __init__(self, rooms, mep_elements):
        """ Initialize the IntersectionChecker with two sets of elements.

        Args:
            rooms (list): A list of room elements.
            mep_elements (list): A list of MEP elements.
        """
        self.rooms = rooms
        self.mep_elements = mep_elements
        self.total_rooms = len(rooms)
        self.total_mep_elements = len(mep_elements)
        self.match_set = []

        # Statistics tracking
        self.successful_mappings = 0
        self.empty_rooms = 0
        self.rooms_with_mep = 0
        self.unmapped_mep_elements = []
        self.rooms_processed = 0

    def check_intersection(self, transform):
        # type: (DB.Transform) -> None
        """ Check for intersections between rooms and MEP elements and track statistics.

        Args:
            transform: The transformation to apply to room bounding boxes.
        """

        mep_element_ids = List[DB.ElementId]([i.Id for i in self.mep_elements])
        mapped_mep_ids = set()

        for room in self.rooms:
            self.rooms_processed += 1
            room_has_mep = False

            try:
                origin_bbox = room.get_BoundingBox(None)
                if origin_bbox is None:
                    continue

                min_point = transform.OfPoint(origin_bbox.Min)
                max_point = transform.OfPoint(origin_bbox.Max)

                outline = DB.Outline(min_point, max_point)
                bbox_filter = DB.BoundingBoxIntersectsFilter(outline)  # type: DB.BoundingBoxIntersectsFilter

                mep_elements_in_bbox = DB.FilteredElementCollector(HOST_APP.doc, mep_element_ids) \
                    .WhereElementIsNotElementType() \
                    .WherePasses(bbox_filter) \
                    .ToElements()

                for mep_element in mep_elements_in_bbox:
                    try:
                        if are_elements_intersect(room, mep_element, largest_solid_only=True):
                            self.match_set.append((room, mep_element))
                            mapped_mep_ids.add(mep_element.Id.IntegerValue)
                            room_has_mep = True
                    except Exception:
                        # Silent error handling - don't print exceptions to output
                        pass

                # Track room statistics
                if room_has_mep:
                    self.rooms_with_mep += 1
                else:
                    self.empty_rooms += 1

            except Exception:
                # Silent error handling for room processing
                pass

        # Track unmapped MEP elements
        for mep_element in self.mep_elements:
            if mep_element.Id.IntegerValue not in mapped_mep_ids:
                self.unmapped_mep_elements.append(mep_element)
                
                
    def set_parameters(self, source_param, target_param):
        """Set the source and target parameters for the intersection check and track success/failure.

        Args:
            source_param (ParamDef): The parameter definition for the source.
            target_param (ParamDef): The parameter definition for the target.
        """

        for room, mep_element in self.match_set:
            try:
                if source_param and target_param:
                    source = get_parameter(source_param, room)
                    target = get_parameter(target_param, mep_element)

                    if source and target and not target.IsReadOnly:
                        target.Set(source.AsString())
                        self.successful_mappings += 1
            except Exception:
                pass

    def get_statistics(self):
        """Get comprehensive statistics about the intersection checking process.

        Returns:
            dict: Dictionary containing all statistics
        """
        return {
            'total_rooms': self.total_rooms,
            'total_mep_elements': self.total_mep_elements,
            'rooms_processed': self.rooms_processed,
            'successful_mappings': self.successful_mappings,
            'empty_rooms': self.empty_rooms,
            'rooms_with_mep': self.rooms_with_mep,
            'unmapped_mep_elements': self.unmapped_mep_elements,
            'total_intersections_found': len(self.match_set)
        }