from DCMvn.core import DB, HOST_APP
from DCMvn.forms import alert
from DCMvn.revit.geometry import GetElementMergedSolid, GetElementMeshes

from space_props import GenericSpaceProperties
from factory.curve_factory import CurveFactory
from cached_collector import HOST_COLLECTOR

class GenericSpace:
    def __init__(self, generic_space):
        # type: (DB.Element) -> GenericSpace
        """Initialize a GenericSpace instance.
        This class is used to represent a generic space in the system.
        Args:
            generic_space (DB.Element): The generic space element from the linked model.
        """
        self.__space = generic_space
        self.__geometry = self.__get_geometry()
        self.__curve_arrays, self.__base_offset, self.__limit_offset = self.__compute_space_location()

    @property
    def number(self):
        return self.__space.LookupParameter(GenericSpaceProperties.Number).AsString()

    @property
    def name(self):
        return self.__space.LookupParameter(GenericSpaceProperties.Name).AsString()

    @property
    def guid(self):
        return self.__space.get_Parameter(GenericSpaceProperties.Guid).AsString()

    @property
    def level(self):
        level_name = self.__space.LookupParameter(GenericSpaceProperties.Level).AsString()
        return HOST_COLLECTOR.level_dicts[level_name]
    
    @property
    def attached_plan_view(self):
        # type: () -> DB.ViewPlan | None
        """Get the attached plan view of the space.
        
        Returns:
            DB.ViewPlan | None: The attached plan view of the space, or None if not found.
        """
        try:
            return HOST_COLLECTOR.views_dicts[self.level.Name]
        except KeyError:
            alert("No ORI plan view found for the space's level.")
            return None
        
    @property
    def curve_arrays(self):
        # type: () -> list[DB.CurveArray]
        """Get the curve arrays of the space."""
        return self.__curve_arrays

    @property
    def base_offset(self):
        # type: () -> float
        """Get the base offset of the space.

        Returns:
            float: The base offset of the space in feet.
        """
        return self.__base_offset

    @property
    def limit_offset(self):
        # type: () -> float
        """Get the limit offset of the space.

        Returns:
            float: The limit offset of the space in feet.
        """
        return self.__limit_offset

    @property
    def upper_limit(self):
        # type: () -> DB.ElementId
        """Get the upper limit of the space.

        Returns:
            DB.ElementId: The upper level id of the space.
        """
        return self.__upper_limit
    
    def __get_geometry(self):
        # type: () -> DB.Solid | list[DB.Mesh]
        """Get the geometry of the space.
        This method retrieves the geometry of the space, either as a solid or as meshes.
        Returns:
            DB.Solid | list[DB.Mesh]: The geometry of the space, either as a solid or a list of meshes.
        """
        solid = GetElementMergedSolid(self.__space)
        if solid is None:
            meshes = GetElementMeshes(self.__space)
            return meshes[0]
        return solid
    
    def __compute_space_location(self):
        # type: () -> list[DB.CurveArray]
        """Get the curve arrays of the space.
        This method retrieves the curve arrays of the space, which are used for boundary representation.
        Returns:
            list[DB.CurveArray]: The curve arrays of the space.
        """
        if isinstance(self.__geometry, DB.Solid):
            space_curves_groups = CurveFactory.get_boundary_curves_from_solid(self.__geometry, tolerance=0.001)
            top_space_curves_groups = CurveFactory.get_boundary_curves_from_solid(self.__geometry, tolerance=0.001, bottom=False)
        else:
            space_curves_groups = CurveFactory.get_boundary_curves_from_mesh(self.__geometry, tolerance=0.001)
            top_space_curves_groups = CurveFactory.get_boundary_curves_from_mesh(self.__geometry, tolerance=0.001, bottom=False)
        
        # Base offset
        level = self.level
        bottom_generic_space = space_curves_groups[0][0].GetEndPoint(0).Z if space_curves_groups else 0.0
        base_offset = level.Elevation - bottom_generic_space if bottom_generic_space < level.Elevation else 0.0
        
        # Limit offset
        top_generic_space = top_space_curves_groups[0][0].GetEndPoint(0).Z if top_space_curves_groups else 0.0
        limit_offset = top_generic_space - level.Elevation
        
        # CurveArray
        curveArrays = []
        for curves in space_curves_groups:
            curveArray = DB.CurveArray()
            for curve in curves:
                curveArray.Append(curve)
            curveArrays.append(curveArray)
        
        return curveArrays, base_offset, limit_offset
            
        