# coding: utf8
import clr

from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import System
from DCMvn.forms import alert
from pyrevit import script

from lib.factory.curve_factory import CurveFactory
from lib.factory.spatial_factory import SpatialFactory
from lib.cached_collector import HOST_COLLECTOR
from lib.generic_space import GenericSpace

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

if __shiftclick__: # noqa: F821
    result = alert("Automatically create spaces from linked generic spaces?\n\n"
                   "This take time, are you sure you want to continue?",
                   warn_icon=True, yes=True, no=True)
    if not result:
        script.exit()

    with DB.Transaction(doc, "Create Space") as t:
        t.Start()
        
        generics_spaces = HOST_COLLECTOR.linked_generic_spaces
        for generic_space in generics_spaces:
            space = GenericSpace(generic_space)
            try:
                location = CurveFactory.get_point_inside_largest_curve_array(space.curve_arrays)
                
                # Create separators
                SpatialFactory.create_separators(space, create_space=True)
            
                # Create space
                new_space = SpatialFactory.create_space(space, location)
                
                # Map properties
                SpatialFactory.map_properties(new_space, space)
            except: # noqa: E722
                continue
        
        t.Commit()
else:
    from archive.space_export import main
    main()
