import clr

from DCMvn.core import DB, HOST_APP, UI
from DCMvn.revit.selection import DSelection
from DCMvn.core.framework import List, System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

MreProjectIdentifier = "0061721_MRE"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
LevelParameter = "AW_ARC.Geschossnummer"
SpaceNameParameter = "AW_ARC.Raumname"
SpaceNumberParameter = "AW_ARC.Raumnummer ARC"

link_space = DSelection(is_linked=True).pick()
space = DSelection(is_linked=False).pick()

# map link to space (Name, Number, IfcGUID)

def map_link_to_space(link_space, space):
    if not link_space or not space:
        return None

    link_space_name = link_space.LookupParameter(SpaceNameParameter).AsString()
    link_space_number = link_space.LookupParameter(SpaceNumberParameter).AsString()
    link_space_guid = link_space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
    
    space_name = space.get_Parameter(DB.BuiltInParameter.ROOM_NAME).Set(link_space_name)
    space_number = space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).Set(link_space_number)
    space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(link_space_guid)


with DB.Transaction(doc, "Map Link to Space") as t:
    t.Start()
    map_link_to_space(link_space, space)
    t.Commit()



