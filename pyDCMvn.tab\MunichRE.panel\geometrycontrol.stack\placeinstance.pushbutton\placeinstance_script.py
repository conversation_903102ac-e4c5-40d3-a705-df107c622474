# -*- coding: utf-8 -*-

# Name of the button in Revit
__title__ = "02_PlacingFamily_ByExcel"
__doc__ = """Version = 1.6
----------------------------------------------------------------
Description:
This is a tool for placing Family Instance based on Excel file having location
----------------------------------------------------------------
How-to:
Step 1: Click on the button (Shift + Click on the button to Change Parameter to input data from GlobalId column [Default: IfcGUID])
Step 2: Select Excel file with location data
Step 3: Ensure all Family Name and Type Name are in RVT.FamilyName and RVT.TypeName have been loaded into the project.

----------------------------------------------------------------
Last update:
- [08.11.2024] - 1.6 Release - Added Select Sheet from List
----------------------------------------------------------------
Author: Long Dang"""

# Imports
import sys
import os
import datetime
import re
from math import pi
import clr
import ast
import System

clr.AddReference("System")
from System.Collections.Generic import List
from System import GC

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.DB.Structure import StructuralType
from Autodesk.Revit.Exceptions import ArgumentsInconsistentException
from pyrevit import forms, script
from DCMvn.forms import wpfforms

# Use xlrd for reading Excel files (compatible with IronPython 2.7)
import xlrd
import xlsxwriter

from DCMvn.coreutils.assemblyhandler import load_miniexcel
load_miniexcel()
from MiniExcelLibs import MiniExcel

# Custom Category Mapping Module
from MunichRE.BuiltinCategory_mapping import category_mapping
from MunichRE.constants import *

# RPW Imports
from rpw.ui.forms import (
    FlexForm,
    Label,
    ComboBox,
    TextBox,
    Separator,
    Button,
    CheckBox,
    SelectFromList,
)

# Variables
logger = script.get_logger()
doc = __revit__.ActiveUIDocument.Document  # type: Document
uidoc = __revit__.ActiveUIDocument  # type: UIDocument
app = __revit__.Application  # Application class
selection = uidoc.Selection  # type: Selection
active_view = doc.ActiveView  # Get Currently open View
output = script.get_output()
active_level = active_view.GenLevel  # Only View plan views have associated Level.
rvt_year = int(app.VersionNumber)  # Get Version number of Revit file using script
PATH_SCRIPT = os.path.dirname(__file__)  # Absolute path to the folder where script is located

# Constants for easy configuration

MISSING_FAMILY_REPORT = set()  # Using a set to capture unique missing family/type details
RECTANGULAR_HEIGHT = "KBP.[h] Hoehe"
RECTANGULAR_LENGTH = ["KBP.[l] Laenge","KBP.[L] Länge","KBP.[l] Länge","KBP.[l] Länge (l)","KBP.[PL] Passlänge","KBP.[a] Länge","KBP.[a] a"]


def to_internal_units(value_):
    return UnitUtils.ConvertToInternalUnits(value_, UnitTypeId.Millimeters)


# Functions
def read_excel_file(file_path_, sheet_name_, header_row_=0):
    """Read data from an Excel file using xlrd."""
    try:
        workbook = xlrd.open_workbook(file_path_)
        worksheet = workbook.sheet_by_name(sheet_name_)
        parameter_names = worksheet.row_values(header_row_)
        data = []

        for row_idx in range(header_row_ + 1, worksheet.nrows):
            row_values = worksheet.row_values(row_idx)
            row_data = dict((parameter_names[i], row_values[i]) for i in range(len(parameter_names)))
            data.append(row_data)

        return data, parameter_names
    except Exception as e:
        forms.alert("Error reading Excel file: {}".format(e), exitscript=True)
        return [], []


def write_failed_instances_report(file_path_, headers_, failed_instances_):
    """Write failed rotations to an Excel file using xlsxwriter."""
    try:
        # Create a workbook and add a worksheet
        workbook = xlsxwriter.Workbook(file_path_)
        worksheet = workbook.add_worksheet("Failed_NoRouting")

        # Write headers to the first row
        for col, header in enumerate(headers_ + ["ErrorType"]):  # Adding "ErrorType" column
            worksheet.write(0, col, header)

        # Write each failed instance data row
        for row_idx, instance_data in enumerate(failed_instances_, start=1):
            for col_idx, header in enumerate(headers_ + ["ErrorType"]):
                worksheet.write(row_idx, col_idx, instance_data.get(header, ""))

        # Close the workbook
        workbook.close()
        logger.info("Failed rotations report saved to: {}".format(file_path_))
    except Exception as e:
        logger.error("Error writing failed instances report: {}".format(e))


def parse_xyz(xyz_string_):
    """Parse an XYZ string in the format '(x, y, z)' without assuming any prefix."""
    try:
        match = re.match(
            r"\(?\s*([-+]?[0-9]*\.?[0-9]+),\s*([-+]?[0-9]*\.?[0-9]+),\s*([-+]?[0-9]*\.?[0-9]+)\s*\)?",
            xyz_string_,
        )
        if not match:
            logger.error("Invalid XYZ format: {}".format(xyz_string_))
            return None

        x, y, z = map(float, match.groups())
        return XYZ(x, y, z)
    except AttributeError:
        logger.error("AttributeError: Invalid XYZ string, cannot extract Z-coordinate.")
        return None
    except Exception as e:
        logger.error("Error parsing XYZ: {}".format(e))
        return None


def activate_family_symbol(doc_, category_name_, family_name_, type_name_):
    """
    Activate a FamilySymbol in the specified category by matching Family Name and Type Name.

    Parameters:
    - doc: The current Revit document.
    - category_name: The name of the category (mapped to a BuiltInCategory).
    - family_name: The name of the family to search for.
    - type_name: The name of the type (symbol) to search for.
    - category_mapping: A dictionary mapping category names to BuiltInCategory values.

    Returns:
    - A tuple (FamilySymbol, None) if successful, or (None, error_message) if not.
    """
    try:
        # Get the BuiltInCategory from the category name
        built_in_category = category_mapping.get(category_name_)
        if not built_in_category:
            return None, "Category '{}' not recognized".format(category_name_)

        # Collect FamilySymbols in the specified category
        symbols = (
            FilteredElementCollector(doc_)
            .OfClass(FamilySymbol)
            .OfCategory(built_in_category)
        )

        # Initialize a flag to track if the family name is found
        family_found = False

        # Iterate through the symbols to find a match
        for symbol in symbols:
            if symbol.Family.Name == family_name_:
                family_found = True  # Mark that the family name exists in the category
                if Element.Name.__get__(symbol) == type_name_:
                    # Activate the symbol if it is not already active
                    if not symbol.IsActive:
                        symbol.Activate()
                        doc_.Regenerate()
                    return symbol, None

        # Return specific messages based on what was found
        if family_found:
            return (
                None,
                "Family '{}' found in Category '{}', but no Type Name '{}' exists.".format(
                    family_name_, category_name_, type_name_
                ),
            )
        else:
            return None, "Family '{}' not found in Category '{}'.".format(
                family_name_, category_name_
            )
    except Exception as e:
        # Handle any exceptions and return an error message
        return None, str(e)


def get_level_dict():
    """
    Retrieve a dictionary mapping level names to Level IDs in the Revit document.
    """
    levels = FilteredElementCollector(doc).OfClass(Level).ToElements()
    level_dict = {level.Name: level.Id for level in levels}  # Map level name to ID
    return level_dict

failed_instances = []
def get_value(param_list, _item, error_message):
    # type: (list, dict, str) -> object
    """ Check if the param_list has only one value in the expando_object

    Args:
        param_list (list): list of string
        _item (dict): dictionary
        error_message (str): error message when the param_list has no value

    Raises:
        ValueError: error when the param_list has more than 1 value
        ValueError: error when the param_list has no value
    """
    unique_value = set()
    param_has_value = []

    for param in param_list:
        value = _item.get(param, None)
        if value:
            param_has_value.append(param)

            # string
            try:
                if isinstance(value, str):
                    value = ast.literal_eval(value)[0]
                    unique_value.add(value)
            except ValueError:
                continue

            # float
            if isinstance(value, float):
                unique_value.add(value)

    if len(unique_value) > 1:
        error_type = "The Instance is placed with rotation. Selected from one of the following input value: {}".format(", ".join(param_has_value))
        logger.warning(error_type)
        _item["ErrorType"] = error_type
        failed_instances.append(_item)
        return unique_value.pop()
    elif not unique_value:
        error_type = (error_message + "Length is not defined in columns: {}".format(", ".join(param_list)))
        logger.warning(error_type)
        _item["ErrorType"] = error_type
        failed_instances.append(_item)
    else:
        return unique_value.pop()

def get_rotation_angle(item_):
    # type: (dict) -> float
    """
    Get the rotation angle from the Excel data row.

    Args:
        item_ (dict): The data row from the Excel file.

    Returns:
        float: The rotation angle in degrees, or None if not found or invalid.
    """
    try:
        # Get the rotation angle from the Excel data row
        if isinstance(RECTANGULAR_LENGTH, list):
            length = get_value(param_list=RECTANGULAR_LENGTH,
                                _item=item_,
                                error_message="The Instance is placed without rotation. ")
        else:
            length = item_.get(RECTANGULAR_LENGTH)

        centroid = XYZ(
            *[
                to_internal_units(val)
                for val in ast.literal_eval(item_.get(CENTROID_MM))
            ]
        )
        transform_x = XYZ(*ast.literal_eval(item_.get(DIRECTION_X)))
        transform_y = XYZ(*ast.literal_eval(item_.get(DIRECTION_Y)))
        transform_z = XYZ(*ast.literal_eval(item_.get(DIRECTION_Z)))
        half_distance = [
            to_internal_units(val)
            for val in ast.literal_eval(item_.get(HALF_DISTANCE_MM))
        ]

        if (
            not length
            or not centroid
            or not transform_x
            or not transform_y
            or not transform_z
            or not half_distance
        ):
            raise ValueError("Missing or invalid data for rotation calculation.")

        # Transform
        transform = Transform.Identity
        transform.Origin = centroid
        transform.BasisX = transform_x
        transform.BasisY = transform_y
        transform.BasisZ = transform_z

        bbox = BoundingBoxXYZ()
        bbox.Transform = transform
        bbox.Min = XYZ(*[-val for val in half_distance])
        bbox.Max = XYZ(*[val for val in half_distance])

        # Calculate the rotation angle
        min_pt = bbox.Min
        max_pt = bbox.Max

        # 8 corners of the oriented bounding box
        _pt0 = min_pt  # noqa
        _pt1 = XYZ(max_pt.X, min_pt.Y, min_pt.Z)
        _pt2 = XYZ(max_pt.X, max_pt.Y, min_pt.Z)
        _pt3 = XYZ(min_pt.X, max_pt.Y, min_pt.Z)
        _pt4 = XYZ(min_pt.X, min_pt.Y, max_pt.Z)
        _pt5 = XYZ(max_pt.X, min_pt.Y, max_pt.Z)
        _pt6 = max_pt
        _pt7 = XYZ(min_pt.X, max_pt.Y, max_pt.Z)

        try:
            edge1 = Line.CreateBound(_pt0, _pt1).CreateTransformed(transform)
            edge2 = Line.CreateBound(_pt0, _pt3).CreateTransformed(transform)
            edge3 = Line.CreateBound(_pt0, _pt4).CreateTransformed(transform)
        except ArgumentsInconsistentException:
            return None

        edge_dict = {_pt1: edge1, _pt3: edge2, _pt4: edge3}

        # Get the length edge
        group_length = (
            edge_dict.items()
            .OrderByDescending(lambda x: abs(x[1].Length - length))  # noqa
            .ToList()
        )
        length_edge = group_length.Last()[1]
        length_edge_points = (
            length_edge.Tessellate()
            .OrderBy(lambda x: x.X)
            .ThenBy(lambda x: x.Y)
            .ThenBy(lambda x: x.Z)
            .ToList()
        )
        length_edge_direction = (
            length_edge_points[1] - length_edge_points[0]
        ).Normalize()

        # Calculate the rotation angle
        reference_axis = XYZ.BasisX
        angle = length_edge_direction.AngleTo(reference_axis)
        cross = reference_axis.CrossProduct(length_edge_direction)

        if cross.Z < 0:
            angle = -angle

        # print("{} Angle: ".format(item_.get(GLOBAL_ID)), angle * 180 / pi)

        return angle
    except Exception as e:
        # logger.error("{} - Rotation Error: {}".format(item_.get(GLOBAL_ID), e))
        return None


def place_family_instance_with_rotation(
    doc_, family_symbol_, location_, level_name_, z_coordinate_, item_
):
    """
    Place a family instance with rotation at a specific level, converting Z-coordinate to level-relative offset.

    Args:
        doc_ (Document): The active Revit document.
        family_symbol_ (FamilySymbol): The family symbol to place.
        location_ (XYZ): The base location (X and Y) of the instance.
        level_name (str): The name of the level where the instance should be placed.
        z_coordinate_ (float): Z-coordinate from the Excel file (project elevation).
        item_ (dict): The data row from the Excel file.

    Returns:
        FamilyInstance: The placed family instance, or None if placement fails.
        str: An error message if placement fails, or None if successful.
    """
    try:
        # Get the level dictionary
        levels = FilteredElementCollector(doc_).OfClass(Level).ToElements()
        # level_dict = {
        #     level.Name: level for level in levels
        # }  # Map level name to Level object
        level_dict = {level.Name: level for level in levels if level.Name is not None}

        # Find the Level object based on the level name
        level = level_dict.get(level_name_)
        if not level:
            logger.warning("Level '{}' not found in the project.".format(level_name_))
            return None, "Level '{}' not found in the project.".format(level_name_)

        # Calculate the elevation offset relative to the level
        level_elevation = getattr(level, 'Elevation', None)
        if level_elevation is None:
            logger.warning("Level '{}' does not have a valid elevation.".format(level_name_))
            return None, "Level '{}' does not have a valid elevation.".format(level_name_)

        relative_offset = z_coordinate_ - level_elevation  # Convert Z to level reference offset

        # Set the location Z to the relative offset
        adjusted_location = XYZ(location_.X, location_.Y, relative_offset)

        # Ensure the family symbol is activated
        if not family_symbol_.IsActive:
            family_symbol_.Activate()
            doc_.Regenerate()

        # Place the family instance at the level with offset
        instance_ = doc_.Create.NewFamilyInstance(
            adjusted_location, family_symbol_, level, StructuralType.NonStructural
        )
        if instance_:
        # Get the rotation angle from the Excel data row
            rotation_angle = get_rotation_angle(item_)
            if rotation_angle:
                try:
                    axis = Line.CreateBound(
                        adjusted_location,
                        XYZ(
                            adjusted_location.X,
                            adjusted_location.Y,
                            adjusted_location.Z + 1,
                        ),
                    )
                    ElementTransformUtils.RotateElement(
                        doc_, instance_.Id, axis, rotation_angle
                    )
                except AttributeError:
                    logger.warning("Rotation skipped due to invalid rotation axis or parameters.")
        
        return instance_, None

    except AttributeError as ae:
        logger.error("AttributeError: {}".format(ae))
        return None, "AttributeError: {}".format(ae)
    except Exception as e:
        logger.error("Error placing family instance: {}".format(e))
        return None, str(e)


# Main code
if __shiftclick__:  # Shift + Click # noqa
    PARAMETER_NAME_FOR_CODE = forms.ask_for_string(
        default="IfcGUID",
        prompt="Enter Parameter name:",
        title="Get Code Number From Parameter Name",
    )
else:
    PARAMETER_NAME_FOR_CODE = "IfcGUID"

excel_file_path = forms.pick_excel_file()
if not excel_file_path:
    forms.alert(
        "Excel | Can't find Excel path for Get Data. Please Try Again", exitscript=True
    )

# Get all sheet names from the selected Excel file
sheet_names = MiniExcel.GetSheetNames(excel_file_path)

select_sheet = wpfforms.SelectFromList.show(sheet_names, "Select Sheet from Workbook")
if not select_sheet:
        wpfforms.alert("Excel | No sheet selected. Please try again.", exitscript=True)

datas, headers = read_excel_file(excel_file_path, select_sheet)

placed_elements_count = 0
start = datetime.datetime.now()

with forms.ProgressBar(title="Place Families", cancellable=True) as pb:
    with Transaction(doc, "Place Families") as transaction:
        transaction.Start()
        for counter, item in enumerate(datas,1):
            try:
                pb.update_progress(counter, len(datas))
                if pb.cancelled:
                    logger.info("Operation cancelled by user.")
                    break

                # Parse location and direction vectors
                location_raw = item.get(CENTROID)

                # Extract family name and category name
                family_name = item.get(RVT_FAMILY_NAME)
                type_name = item.get(RVT_TYPE_NAME)
                category_name = item.get(RVT_CATEGORY)
                storey_name = item.get(STOREY_NAME)  # New column for level name
                z_coordinate = (
                    parse_xyz(location_raw).Z if location_raw else None
                )  # Extract Z-coordinate

                # Validate required fields
                if not family_name or not category_name or not type_name:
                    logger.warning(
                        "Missing required fields: Family Name or Category Name or Type Name. Skipping row."
                    )
                    item["ErrorType"] = item.get("ErrorType", "") + "Missing Family Name or Category Name or Type Name;"
                    failed_instances.append(item)
                    continue

                # Log raw input for debugging
                logger.info(
                    "Raw Data - Location: {0},Category: {1}, Family: {2},Type: {3} , Storey: {4}, Z: {5}".format(
                        location_raw,
                        category_name,
                        family_name,
                        type_name,
                        storey_name,
                        z_coordinate,
                    )
                )

                # Convert parsed data to XYZ objects
                location = parse_xyz(location_raw)

                # Skip invalid rows
                if not location:
                    item["ErrorType"] = item.get("ErrorType", "") + "Invalid or missing XYZ directions;"
                    failed_instances.append(item)
                    continue

                # Place the family instance
                family_symbol, error = activate_family_symbol(
                    doc, category_name, family_name, type_name
                )
                if not family_symbol:
                    item["ErrorType"] = item.get("ErrorType", "") + str(error)
                    failed_instances.append(item)
                    continue

                instance, error = place_family_instance_with_rotation(
                    doc, family_symbol, location, storey_name, z_coordinate, item
                )

                if not instance:
                    item["ErrorType"] = item.get("ErrorType", "") + str(error)
                    failed_instances.append(item)
                    continue

                if instance:
                    # Set parameter value
                    code_param = instance.LookupParameter(PARAMETER_NAME_FOR_CODE)
                    if code_param:
                        code_param.Set(item.get(GLOBAL_ID, ""))
                placed_elements_count += 1
            except AttributeError as e:
                logger.error("AttributeError encountered: {}".format(e))
                item["ErrorType"] = item.get("ErrorType", "") + "AttributeError encountered during processing;"
                failed_instances.append(item)
            except Exception as e:
                logger.error("Unexpected error: {}".format(e))
                item["ErrorType"] = item.get("ErrorType", "")  + "Unexpected error during processing;"
                failed_instances.append(item)
        transaction.Commit()


if failed_instances:
    report_dir = os.path.dirname(excel_file_path)
    report_name = (
        os.path.splitext(os.path.basename(excel_file_path))[0]
        + "_NoRoutingReport.xlsx"
    )
    report_file_path = os.path.join(report_dir, report_name)
    write_failed_instances_report(report_file_path, headers, failed_instances)
    print(
        "Report for failed rotation elements saved to: {report_file_path}".format(
            report_file_path=report_file_path
        )
    )
else:
    print("No failed instances. No report generated.")

print("Total elements placed: {} out of {}. There are {} elements failed.".format(placed_elements_count, len(datas), len(datas)-placed_elements_count))
print("DONE: {}".format(__title__))
end = datetime.datetime.now()
print("Time elapsed: {}".format(end - start))
